version: '3.8'

services:
  centrifugo:
    image: centrifugo/centrifugo:v5
    container_name: back-talk-centrifugo
    ports:
      - "8000:8000"  # HTTP API and admin web interface
      - "8001:8001"  # WebSocket endpoint
    volumes:
      - ./centrifugo/config.json:/centrifugo/config.json
    command: centrifugo --config=/centrifugo/config.json
    environment:
      - CENTRIFUGO_TOKEN_HMAC_SECRET_KEY=${CENTRIFUGO_TOKEN_HMAC_SECRET_KEY:-your-secret-key-here}
      - CENTRIFUGO_API_KEY=${CENTRIFUGO_API_KEY:-your-api-key-here}
      - CENTRIFUGO_ADMIN_PASSWORD=${CENTRIFUGO_ADMIN_PASSWORD:-admin123}
    restart: unless-stopped
    networks:
      - back-talk-network

networks:
  back-talk-network:
    driver: bridge
