generator client {
  provider        = "prisma-client-js"
  output          = "../generated/prisma"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  extensions = [uuid_ossp(map: "uuid-ossp", schema: "public")]
}

enum PhoneNumberType {
  AGENT
  LEAD
  PERSONAL
}

enum PhoneNumberStatus {
  ACTIVE
  INACTIVE
  PENDING
  DELETED
}

enum AgentStatus {
  ACTIVE
  INACTIVE
  PENDING
  DELETED
}

enum CallStatus {
  RINGING
  IN_PROGRESS
  COMPLETED
  FAILED
  BUSY
  NO_ANSWER
  CANCELED
}

enum CallDirection {
  INBOUND
  OUTBOUND
}

model phone_number {
  id            String            @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  number        BigInt
  status        PhoneNumberStatus @default(ACTIVE)
  monthly_cost  Decimal?          @db.Decimal(10, 5)
  purchase_date DateTime?         @db.Timestamp(6)
  created_at    DateTime          @default(now()) @db.Timestamp(6)
  updated_at    DateTime          @default(now()) @db.Timestamp(6)

  // Relations
  agents       agent[]   @relation("PhoneNumber")
  leads        lead[]    @relation("PhoneNumber")
  messages     message[] @relation("PhoneNumber")
  forwarded_to agent[]   @relation("ForwardNumber")

  @@index([number], map: "phone_number_number_index")
}

model agent {
  id                String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  label             String?
  status            AgentStatus @default(ACTIVE)
  phone_number_id   String      @db.Uuid
  client_id         String      @db.Uuid
  forward_number_id String?     @db.Uuid
  created_at        DateTime    @default(now()) @db.Timestamp(6)
  updated_at        DateTime    @default(now()) @db.Timestamp(6)

  // Relations
  auto_responses auto_response[]
  phone_number   phone_number    @relation("PhoneNumber", fields: [phone_number_id], references: [id])
  forward_number phone_number?   @relation("ForwardNumber", fields: [forward_number_id], references: [id])
  client         client          @relation(fields: [client_id], references: [id])
  conversations  conversation[]
  voicemails     Voicemail[]
  calls          call[]
  messages       message[]

  @@unique([client_id, phone_number_id], map: "agent_phone_client_phone_number_unique")
  @@index([client_id], map: "business_phone_client_id_index")
  @@index([phone_number_id], map: "business_phone_phone_number_id_index")
}

model lead {
  id              String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  phone_number_id String   @db.Uuid
  client_id       String   @db.Uuid
  person_id       String   @db.Uuid
  notes           String?
  created_at      DateTime @default(now()) @db.Timestamp(6)
  updated_at      DateTime @default(now()) @db.Timestamp(6)

  // Relations
  phone_number  phone_number   @relation("PhoneNumber", fields: [phone_number_id], references: [id])
  person        person?        @relation("Person", fields: [person_id], references: [id])
  client        client         @relation(fields: [client_id], references: [id])
  conversations conversation[]
  calls         call[]

  @@unique([client_id, phone_number_id], map: "lead_phone_client_phone_number_unique")
  @@index([phone_number_id], map: "lead_phone_phone_number_id_index")
  @@index([client_id], map: "lead_phone_client_id_index")
  @@index([person_id], map: "lead_person_id_index")
}

model person {
  id         String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name       String?
  email      String?  @db.VarChar(255)
  client_id  String   @db.Uuid
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @default(now()) @db.Timestamp(6)

  // Relations
  leads    lead[]    @relation("Person")
  messages message[]
  client   client    @relation(fields: [client_id], references: [id])
  User     User[]

  @@index([name], map: "person_name_index")
  @@index([client_id], map: "person_client_id_index")
}

model call {
  id          String        @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  agent_id    String        @db.Uuid
  lead_id     String        @db.Uuid
  client_id   String        @db.Uuid
  call_sid    String        @unique // Twilio's unique call identifier
  direction   CallDirection
  status      CallStatus    @default(RINGING)
  from_number String // The calling number (E.164 format)
  to_number   String // The called number (E.164 format)
  duration    Int? // Call duration in seconds
  answered_by String? // "human" or "machine" detection
  price       Decimal?      @db.Decimal(10, 5)
  price_unit  String? // USD, etc.
  started_at  DateTime      @default(now()) @db.Timestamp(6)
  answered_at DateTime?     @db.Timestamp(6)
  ended_at    DateTime?     @db.Timestamp(6)
  created_at  DateTime      @default(now()) @db.Timestamp(6)
  updated_at  DateTime      @default(now()) @db.Timestamp(6)

  // Relations
  agent     agent      @relation(fields: [agent_id], references: [id])
  lead      lead       @relation(fields: [lead_id], references: [id])
  client    client     @relation(fields: [client_id], references: [id])
  voicemail Voicemail? @relation("CallVoicemail")

  @@index([call_sid], map: "call_call_sid_index")
  @@index([agent_id], map: "call_agent_id_index")
  @@index([lead_id], map: "call_lead_id_index")
  @@index([client_id], map: "call_client_id_index")
  @@index([started_at], map: "call_started_at_index")
}

model business_type {
  id          String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name        String   @unique
  description String?
  created_at  DateTime @default(now()) @db.Timestamp(6)
  updated_at  DateTime @default(now()) @db.Timestamp(6)

  // Relations
  clients client[] @relation("BusinessType")
}

enum CompanySize {
  SOLO // 1 person
  MICRO // 2-10 people
  SMALL // 11-50 people
  MEDIUM // 51-200 people
  LARGE // 201-1000 people
  ENTERPRISE // 1000+ people
}

model client {
  id               String       @id(map: "client_pk") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name             String?
  description      String?
  business_type_id String?      @db.Uuid
  company_size     CompanySize?
  is_mobile        Boolean?     @default(false)
  offers_emergency Boolean?     @default(false)
  created_at       DateTime?    @default(now()) @db.Timestamp(6)
  updated_at       DateTime?    @default(now()) @db.Timestamp(6)

  // Relations
  agents        agent[]
  leads         lead[]
  conversations conversation[]
  messages      message[]
  generation    generation[]
  calls         call[]
  business_type business_type? @relation("BusinessType", fields: [business_type_id], references: [id])
  person        person[]
  User          User[]

  @@index([business_type_id], map: "client_business_type_id_index")
}

enum AutoRespondResponseDelay {
  IMMEDIATE
  RANDOM_SECONDS
  ONE_MINUTE
  FIVE_MINUTES
  TEN_MINUTES
  THIRTY_MINUTES
  RANDOM_MINUTES
  ONE_HOUR
}

model auto_response {
  id             String                   @id(map: "auto_respond_pk") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  agent_id       String                   @db.Uuid
  message_prompt String
  enabled        Boolean?                 @default(true)
  response_delay AutoRespondResponseDelay @default(RANDOM_SECONDS)
  created_at     DateTime?                @default(now()) @db.Timestamp(6)

  // Relations
  agent agent @relation(fields: [agent_id], references: [id])

  @@index([agent_id], map: "auto_response_agent_id_index")
}

model conversation {
  id                      String    @id(map: "conversation_pk") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  agent_id                String    @db.Uuid
  lead_id                 String    @db.Uuid
  client_id               String    @db.Uuid
  carrier_conversation_id String?   @db.VarChar(255)
  status                  String?   @db.VarChar(255)
  created_at              DateTime  @default(now()) @db.Timestamp(6)
  updated_at              DateTime  @default(now()) @db.Timestamp(6)
  deleted_at              DateTime? @db.Timestamp(6)
  is_agent_active        Boolean   @default(true)

  // Relations
  agent    agent     @relation(fields: [agent_id], references: [id])
  lead     lead      @relation(fields: [lead_id], references: [id])
  client   client    @relation(fields: [client_id], references: [id])
  messages message[]

  @@index([agent_id], map: "conversation_agent_id_index")
  @@index([lead_id], map: "conversation_lead_id_index")
  @@index([carrier_conversation_id], map: "conversation_carrier_conversation_id_index")
  @@index([client_id], map: "conversation_client_id_index")
}

model message {
  id                String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  conversation_id   String   @db.Uuid
  client_id         String   @db.Uuid
  phone_number_id   String   @db.Uuid
  body              String?
  created_at        DateTime @default(now()) @db.Timestamp(6)
  status            String   @db.VarChar(255)
  direction         String   @db.VarChar(255)
  is_read           Boolean  @default(false)
  generation_id     String?  @db.Uuid
  cost              Decimal  @default(0.000000000) @db.Decimal(10, 9)
  created_by_agent  String?  @db.Uuid
  created_by_person String?  @db.Uuid

  // Relations
  conversation conversation @relation(fields: [conversation_id], references: [id])
  client       client       @relation(fields: [client_id], references: [id])
  phone_number phone_number @relation("PhoneNumber", fields: [phone_number_id], references: [id])
  person       person?      @relation(fields: [created_by_person], references: [id])
  agent        agent?       @relation(fields: [created_by_agent], references: [id])

  @@index([conversation_id], map: "message_conversation_id_index")
  @@index([phone_number_id], map: "message_phone_number_id_index")
  @@index([client_id], map: "message_client_id_index")
}

model generation {
  id         String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  client_id  String   @db.Uuid
  model      String   @db.VarChar(255)
  prompt     String?
  response   String?
  cost       Decimal  @default(0.000000000) @db.Decimal(10, 9)
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @default(now()) @db.Timestamp(6)

  // Relations
  client client @relation(fields: [client_id], references: [id])

  @@index([client_id], map: "generation_client_id_index")
}

model Voicemail {
  id                String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  call_id           String   @unique @db.Uuid // Link to the call record
  recording_sid     String?
  recording_url     String?
  transcription_sid String?
  transcription     String?
  duration          Int      @default(0)
  created_at        DateTime @default(now())

  // Relations
  call    call    @relation("CallVoicemail", fields: [call_id], references: [id])
  agent   agent?  @relation(fields: [agentId], references: [id])
  agentId String? @db.Uuid

  @@map("voicemails")
}

model User {
  id         String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  email      String   @unique
  password   String
  person_id  String?  @db.Uuid
  client_id  String?  @db.Uuid
  role       String   @default("USER") @db.VarChar(255)
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @default(now()) @db.Timestamp(6)

  // Relations
  person person? @relation(fields: [person_id], references: [id])
  client client? @relation(fields: [client_id], references: [id])

  @@index([email], map: "user_email_index")
  @@index([person_id], map: "user_person_id_index")
  @@index([client_id], map: "user_client_id_index")
}
