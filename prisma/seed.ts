import {AutoRespondR<PERSON>po<PERSON><PERSON><PERSON><PERSON>, PhoneNumber<PERSON>tatus, PrismaClient} from "../generated/prisma";

const prisma = new PrismaClient();
const clientId = '90d91234-2c83-4c5e-9624-50b1adfe6a87';
const number1Id = 'f220f9a6-4083-4b54-b20b-f1c629b05ae5';
const number2Id = '3de64ff6-c4ab-4956-ae77-0267b0e946a6';
const number3Id = '7518e8a0-d551-454e-8844-fbf21da8a8ea';
const number4Id = 'b78baffe-af58-4614-89d3-1edd2c1e2c14';
const agent1Id = 'fed3ba38-29f4-4f77-b1a9-df9fb1b7f05f';
const agent2Id = '55c7176b-4fb7-47b9-8cd9-81ce83f0fbb5';
const lead1Id = '5de9455f-a850-43e2-a21c-16d053125aa9';
const autoResponse1Id = '58f059a6-ba3b-436d-9491-0c349bac1874';
const conversation1Id = '1a57ea56-679e-4fe5-8203-03d1b733afc1';
const message1Id = 'f79d3569-803b-4344-ae57-69be49307d03';
const message2Id = 'ff1cc08a-479c-4951-adca-8ae2a09753e1';
const message3Id = '2ce4dd9c-c489-4121-a8e0-71fff57e00a4';
const message4Id = '5fa0aefc-a648-4e52-aa67-61268e8dfaa8';
const message5Id = 'c493031c-a31e-4ebf-9ec3-45aee715c05f';
const plumbingId = 'b1d7e1a0-5c1a-4e1f-8c1a-5c1a4e1f8c1a';

async function main() {
    // Create business types
    const businessTypes = await prisma.business_type.createMany({
        skipDuplicates: true,
        data: [
            {
                id: plumbingId,
                name: 'Plumbing',
                description: 'Plumbing services including repairs, installations, and maintenance',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Electrical',
                description: 'Electrical services including repairs, installations, and maintenance',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'HVAC',
                description: 'Heating, ventilation, and air conditioning services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Landscaping',
                description: 'Landscaping and lawn care services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Cleaning',
                description: 'Residential and commercial cleaning services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Roofing',
                description: 'Roof installation, repair, and maintenance services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Carpentry',
                description: 'Carpentry and woodworking services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Painting',
                description: 'Interior and exterior painting services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Flooring',
                description: 'Flooring installation and repair services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Pest Control',
                description: 'Pest control and extermination services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Locksmith',
                description: 'Locksmith and security services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Moving',
                description: 'Residential and commercial moving services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Appliance Repair',
                description: 'Repair and maintenance of household appliances',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Window Cleaning',
                description: 'Residential and commercial window cleaning services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Garage Door',
                description: 'Garage door installation, repair, and maintenance',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Tree Service',
                description: 'Tree trimming, removal, and maintenance services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Handyman',
                description: 'General home repair and maintenance services',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Concrete',
                description: 'Concrete installation, repair, and maintenance',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Fencing',
                description: 'Fence installation, repair, and maintenance',
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                name: 'Drywall',
                description: 'Drywall installation, repair, and finishing',
                created_at: new Date(),
                updated_at: new Date(),
            },
        ]
    });

    const vertigolabs = await prisma.client.upsert({
        where: { id: clientId },
        update: {
            business_type_id: plumbingId
        },
        create: {
            id: clientId,
            name: 'Vertigo Labs',
            business_type_id: plumbingId
        }
    });

    const phoneNumbers = await prisma.phone_number.createMany({
        data: [
            {
                id: number1Id,
                number: ***********,
                status: PhoneNumberStatus.ACTIVE,
            },
            {
                id: number2Id,
                number: ***********,
                status: PhoneNumberStatus.ACTIVE,
            },
            {
                id: number3Id,
                number: ***********,
                status: PhoneNumberStatus.ACTIVE,
            },
            {
                id: number4Id,
                number: ***********,
                status: PhoneNumberStatus.ACTIVE,
            },
        ]
    });

    const agents = await prisma.agent.createMany({
        data: [
            {
                id: agent1Id,
                label: 'cincinnati agent',
                phone_number_id: number1Id,
                client_id: clientId,
                forward_number_id: number3Id,
                monthly_cost: 1.99,
                purchase_date: new Date('2023-10-01T00:00:00.000Z'),
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                id: agent2Id,
                label: 'columbus agent',
                phone_number_id: number2Id,
                client_id: clientId,
                monthly_cost: 1.99,
                purchase_date: new Date('2025-04-12T00:00:00.000Z'),
                created_at: new Date(),
                updated_at: new Date(),
            }
        ]
    });

    const leads = await prisma.lead.createMany({
        data: [
            {
                id: lead1Id,
                phone_number_id: number4Id,
                client_id: clientId,
                name: 'John Doe',
                email: '<EMAIL>',
                notes: 'Interested in product X',
                created_at: new Date('2024-10-20T00:00:00.000Z'),
                updated_at: new Date(),
            }
        ]
    });

    const auto_responses = await prisma.auto_response.createMany({
        data: [
            {
                id: autoResponse1Id,
                agent_id: agent2Id,
                enabled: true,
                message_prompt: 'Thank you for contacting us! We will get back to you shortly.',
                response_delay: AutoRespondResponseDelay.RANDOM_SECONDS, // in seconds
                created_at: new Date('2024-05-12T00:00:00.000Z'),
            }
        ]
    });

    const conversations = await prisma.conversation.createMany({
        data: [
            {
                id: conversation1Id,
                agent_id: agent2Id,
                lead_id: lead1Id,
                client_id: clientId,
                status: 'OPEN',
                created_at: new Date('2024-10-20T00:00:00.000Z'),
                updated_at: new Date(),
            }
        ]
    });

    const messages = await prisma.message.createMany({
        data: [
            {
                id: message1Id,
                conversation_id: conversation1Id,
                client_id: clientId,
                phone_number_id: number1Id,
                body: 'Hello, how can I help you today?',
                created_at: new Date('2024-10-20T00:00:00.000Z'),
                status: 'delivered',
                direction: 'OUTBOUND',
                is_read: true,
                cost: 0.004,
            },
            {
                id: message2Id,
                conversation_id: conversation1Id,
                client_id: clientId,
                phone_number_id: number4Id,
                body: 'Hi, I am interested in your services.',
                created_at: new Date('2024-10-20T00:00:13.000Z'),
                status: 'delivered',
                direction: 'INBOUND',
                is_read: true,
                cost: 0.004,
            },
            {
                id: message3Id,
                conversation_id: conversation1Id,
                client_id: clientId,
                phone_number_id: number1Id,
                body: 'Great! Can you provide more details?',
                created_at: new Date('2024-10-20T00:00:26.000Z'),
                status: 'delivered',
                direction: 'OUTBOUND',
                is_read: true,
                cost: 0.004,
            },
            {
                id: message4Id,
                conversation_id: conversation1Id,
                client_id: clientId,
                phone_number_id: number4Id,
                body: 'Sure, I would like to know more about your pricing.',
                created_at: new Date('2024-10-20T00:00:39.000Z'),
                status: 'delivered',
                direction: 'INBOUND',
                is_read: true,
                cost: 0.004,
            },
            {
                id: message5Id,
                conversation_id: conversation1Id,
                client_id: clientId,
                phone_number_id: number1Id,
                body: 'Our pricing starts at $19.99 per toilet.',
                created_at: new Date('2024-10-20T00:00:52.000Z'),
                status: 'delivered',
                direction: 'OUTBOUND',
                is_read: true,
                cost: 0.004,
            }
        ]
    });
}

main()
    .then(() => {
        console.log('Database seeded successfully');
        return prisma.$disconnect();
    })
    .catch((error) => {
        console.error('Error seeding database:', error);
        return prisma.$disconnect();
    });
