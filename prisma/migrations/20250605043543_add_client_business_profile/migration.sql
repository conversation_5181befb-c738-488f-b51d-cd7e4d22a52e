-- Create CompanySize enum
CREATE TYPE "CompanySize" AS ENUM ('S<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'SMALL', 'MEDIUM', '<PERSON>R<PERSON>', 'ENTERPRISE');

-- AlterTable
ALTER TABLE "client" 
ADD COLUMN "company_size" "CompanySize",
ADD COLUMN "is_mobile" BOOLEAN DEFAULT false,
ADD COLUMN "offers_emergency" BOOLEAN DEFAULT false,
ADD COLUMN "updated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP;