/*
  Warnings:

  - You are about to drop the column `monthly_cost` on the `agent` table. All the data in the column will be lost.
  - You are about to drop the column `purchase_date` on the `agent` table. All the data in the column will be lost.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "AgentStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'PENDING', 'DELETED');

-- AlterTable
ALTER TABLE "agent" DROP COLUMN "monthly_cost",
DROP COLUMN "purchase_date",
ADD COLUMN     "status" "AgentStatus" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "phone_number" ADD COLUMN     "monthly_cost" DECIMAL(10,5),
ADD COLUMN     "purchase_date" TIMESTAMP(6);
