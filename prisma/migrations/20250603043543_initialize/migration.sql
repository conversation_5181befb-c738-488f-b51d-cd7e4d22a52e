-- CreateExtension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "public";

-- CreateEnum
CREATE TYPE "PhoneNumberType" AS ENUM ('AGENT', 'LEAD', 'PERSONAL');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "PhoneNumberStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'PENDING', 'DELETED');

-- CreateEnum
CREATE TYPE "AutoRespondResponseDelay" AS ENUM ('IMMEDIATE', 'RANDOM_SECONDS', 'ONE_MINUTE', 'FIVE_MINUTES', 'TEN_MINUTES', 'THIRTY_MINUTES', 'RANDOM_MINUTES', 'ONE_HOUR');

-- CreateTable
CREATE TABLE "phone_number" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "number" BIGINT NOT NULL,
    "status" "PhoneNumberStatus" NOT NULL DEFAULT 'ACTIVE',
    "label" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "phone_number_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "agent" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "phone_number_id" UUID NOT NULL,
    "client_id" UUID NOT NULL,
    "label" TEXT,
    "forward_number" BIGINT,
    "monthly_cost" DECIMAL(10,5),
    "purchase_date" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "agent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lead" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "phone_number_id" UUID NOT NULL,
    "client_id" UUID NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "notes" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "lead_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "client" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "name" TEXT,
    "created_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "client_pk" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "auto_response" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "agent_id" UUID NOT NULL,
    "message_prompt" TEXT NOT NULL,
    "enabled" BOOLEAN DEFAULT true,
    "response_delay" "AutoRespondResponseDelay" NOT NULL DEFAULT 'RANDOM_SECONDS',
    "created_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "auto_respond_pk" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "conversation" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "agent_id" UUID NOT NULL,
    "lead_id" UUID NOT NULL,
    "client_id" UUID NOT NULL,
    "carrier_conversation_id" VARCHAR(255),
    "status" VARCHAR(255),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMP(6),

    CONSTRAINT "conversation_pk" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "message" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "conversation_id" UUID NOT NULL,
    "client_id" UUID NOT NULL,
    "phone_number_id" UUID NOT NULL,
    "body" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" VARCHAR(255) NOT NULL,
    "direction" VARCHAR(255) NOT NULL,
    "is_read" BOOLEAN NOT NULL DEFAULT false,
    "generation_id" UUID,
    "cost" DECIMAL(10,9) NOT NULL DEFAULT 0.000000000,

    CONSTRAINT "message_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "generation" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "client_id" UUID NOT NULL,
    "model" VARCHAR(255) NOT NULL,
    "prompt" TEXT,
    "response" TEXT,
    "cost" DECIMAL(10,9) NOT NULL DEFAULT 0.000000000,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "generation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "phone_number_number_index" ON "phone_number"("number");

-- CreateIndex
CREATE INDEX "business_phone_client_id_index" ON "agent"("client_id");

-- CreateIndex
CREATE INDEX "business_phone_phone_number_id_index" ON "agent"("phone_number_id");

-- CreateIndex
CREATE UNIQUE INDEX "agent_phone_client_phone_number_unique" ON "agent"("client_id", "phone_number_id");

-- CreateIndex
CREATE INDEX "lead_phone_phone_number_id_index" ON "lead"("phone_number_id");

-- CreateIndex
CREATE INDEX "lead_phone_client_id_index" ON "lead"("client_id");

-- CreateIndex
CREATE UNIQUE INDEX "lead_phone_client_phone_number_unique" ON "lead"("client_id", "phone_number_id");

-- CreateIndex
CREATE INDEX "auto_response_agent_id_index" ON "auto_response"("agent_id");

-- CreateIndex
CREATE INDEX "conversation_agent_id_index" ON "conversation"("agent_id");

-- CreateIndex
CREATE INDEX "conversation_lead_id_index" ON "conversation"("lead_id");

-- CreateIndex
CREATE INDEX "conversation_carrier_conversation_id_index" ON "conversation"("carrier_conversation_id");

-- CreateIndex
CREATE INDEX "conversation_client_id_index" ON "conversation"("client_id");

-- CreateIndex
CREATE INDEX "message_conversation_id_index" ON "message"("conversation_id");

-- CreateIndex
CREATE INDEX "message_phone_number_id_index" ON "message"("phone_number_id");

-- CreateIndex
CREATE INDEX "message_client_id_index" ON "message"("client_id");

-- CreateIndex
CREATE INDEX "generation_client_id_index" ON "generation"("client_id");

-- AddForeignKey
ALTER TABLE "agent" ADD CONSTRAINT "agent_phone_number_id_fkey" FOREIGN KEY ("phone_number_id") REFERENCES "phone_number"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "agent" ADD CONSTRAINT "agent_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead" ADD CONSTRAINT "lead_phone_number_id_fkey" FOREIGN KEY ("phone_number_id") REFERENCES "phone_number"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lead" ADD CONSTRAINT "lead_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auto_response" ADD CONSTRAINT "auto_response_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "agent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "conversation" ADD CONSTRAINT "conversation_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "agent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "conversation" ADD CONSTRAINT "conversation_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "lead"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "conversation" ADD CONSTRAINT "conversation_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message" ADD CONSTRAINT "message_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message" ADD CONSTRAINT "message_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message" ADD CONSTRAINT "message_phone_number_id_fkey" FOREIGN KEY ("phone_number_id") REFERENCES "phone_number"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "generation" ADD CONSTRAINT "generation_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
