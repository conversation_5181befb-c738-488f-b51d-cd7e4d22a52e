-- Create<PERSON><PERSON>
CREATE TYPE "CallStatus" AS ENUM ('RINGING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'BUSY', 'NO_ANSWER', 'CANCELED');

-- CreateEnum
CREATE TYPE "CallDirection" AS ENUM ('INBOUND', 'OUTBOUND');

-- CreateTable
CREATE TABLE "call" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "agent_id" UUID NOT NULL,
    "lead_id" UUID NOT NULL,
    "client_id" UUID NOT NULL,
    "call_sid" TEXT NOT NULL,
    "direction" "CallDirection" NOT NULL,
    "status" "CallStatus" NOT NULL DEFAULT 'RINGING',
    "from_number" TEXT NOT NULL,
    "to_number" TEXT NOT NULL,
    "duration" INTEGER,
    "answered_by" TEXT,
    "price" DECIMAL(10,5),
    "price_unit" TEXT,
    "started_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "answered_at" TIMESTAMP(6),
    "ended_at" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "call_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "voicemails" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "call_id" UUID NOT NULL,
    "recording_sid" TEXT,
    "recording_url" TEXT,
    "transcription_sid" TEXT,
    "transcription" TEXT,
    "duration" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "agentId" UUID,

    CONSTRAINT "voicemails_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "call_call_sid_key" ON "call"("call_sid");

-- CreateIndex
CREATE INDEX "call_call_sid_index" ON "call"("call_sid");

-- CreateIndex
CREATE INDEX "call_agent_id_index" ON "call"("agent_id");

-- CreateIndex
CREATE INDEX "call_lead_id_index" ON "call"("lead_id");

-- CreateIndex
CREATE INDEX "call_client_id_index" ON "call"("client_id");

-- CreateIndex
CREATE INDEX "call_started_at_index" ON "call"("started_at");

-- CreateIndex
CREATE UNIQUE INDEX "voicemails_call_id_key" ON "voicemails"("call_id");

-- AddForeignKey
ALTER TABLE "call" ADD CONSTRAINT "call_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "agent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "call" ADD CONSTRAINT "call_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "lead"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "call" ADD CONSTRAINT "call_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "voicemails" ADD CONSTRAINT "voicemails_call_id_fkey" FOREIGN KEY ("call_id") REFERENCES "call"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "voicemails" ADD CONSTRAINT "voicemails_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "agent"("id") ON DELETE SET NULL ON UPDATE CASCADE;
