-- CreateTable
CREATE TABLE "business_type" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "business_type_pkey" PRIMARY KEY ("id")
);

-- AlterTable
ALTER TABLE "client" ADD COLUMN "business_type_id" UUID;

-- CreateIndex
CREATE UNIQUE INDEX "business_type_name_key" ON "business_type"("name");

-- CreateIndex
CREATE INDEX "client_business_type_id_index" ON "client"("business_type_id");

-- Add<PERSON><PERSON>ign<PERSON><PERSON>
ALTER TABLE "client" ADD CONSTRAINT "client_business_type_id_fkey" FOREIGN KEY ("business_type_id") REFERENCES "business_type"("id") ON DELETE SET NULL ON UPDATE CASCADE;