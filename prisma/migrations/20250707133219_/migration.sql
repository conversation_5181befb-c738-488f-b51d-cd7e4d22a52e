/*
  Warnings:

  - You are about to drop the column `email` on the `lead` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `lead` table. All the data in the column will be lost.
  - You are about to drop the column `notes` on the `lead` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "conversation" ADD COLUMN     "is_agent_acvtive" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "lead" DROP COLUMN "email",
DROP COLUMN "name",
DROP COLUMN "notes",
ADD COLUMN     "person_id" UUID;

-- AlterTable
ALTER TABLE "message" ADD COLUMN     "created_by_agent" UUID,
ADD COLUMN     "created_by_person" UUID;

-- CreateTable
CREATE TABLE "person" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "name" TEXT,
    "email" TEXT,
    "notes" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "person_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "person_name_index" ON "person"("name");

-- CreateIndex
CREATE INDEX "lead_person_id_index" ON "lead"("person_id");

-- AddForeignKey
ALTER TABLE "lead" ADD CONSTRAINT "lead_person_id_fkey" FOREIGN KEY ("person_id") REFERENCES "person"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message" ADD CONSTRAINT "message_created_by_person_fkey" FOREIGN KEY ("created_by_person") REFERENCES "person"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message" ADD CONSTRAINT "message_created_by_agent_fkey" FOREIGN KEY ("created_by_agent") REFERENCES "agent"("id") ON DELETE SET NULL ON UPDATE CASCADE;
