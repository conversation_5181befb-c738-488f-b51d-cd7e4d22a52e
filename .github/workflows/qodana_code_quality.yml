name: <PERSON><PERSON><PERSON>
on:
  workflow_dispatch:
  pull_request:
  push:
    branches: # Specify your branches here
      - main # The 'main' branch
      - 'releases/*' # The release branches

jobs:
  qodana:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      checks: write
    steps:
      - uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}  # to check out the actual pull request commit, not the merge commit
          fetch-depth: 0  # a full history is required for pull request analysis
      - name: 'Qodana Scan'
        uses: JetBrains/qodana-action@v2025.1
        with:
          pr-mode: false
        env:
          QODANA_TOKEN: ${{ secrets.QODANA_TOKEN_663724077 }}
          QODANA_ENDPOINT: 'https://qodana.cloud'