/**
 * Formats a phone number into E.164 format (+1XXXXXXXXXX)
 * @param phoneNumber - The phone number to format (can be number or string)
 * @returns Formatted phone number string in E.164 format
 */
export function formatToE164(phoneNumber: bigint | string): string {
  // Convert to string if it's a number
  let phoneStr = phoneNumber.toString();

  // Remove all non-digit characters
  phoneStr = phoneStr.replace(/\D/g, '');

  // For US numbers (assuming all numbers are US)
  if (phoneStr.length === 10) {
    return `+1${phoneStr}`;
  }

  // If it's already an 11-digit number with country code
  if (phoneStr.length === 11 && phoneStr.startsWith('1')) {
    return `+${phoneStr}`;
  }

  // If it already has a plus sign, return as is
  if (phoneStr.startsWith('+')) {
    return phoneStr;
  }

  // Default case - just add + if it seems to be a complete number
  if (phoneStr.length >= 10) {
    return `+${phoneStr}`;
  }

  // Return original if we can't format it
  return phoneStr;
}

/**
 * Formats a phone number for display in a user-friendly format (XXX) XXX-XXXX
 * @param phoneNumber - The phone number to format (can be number or string)
 * @returns Formatted phone number string
 */
export function formatPhoneForDisplay(phoneNumber: bigint | string): string {
  // Convert to E.164 first to normalize
  const e164 = formatToE164(phoneNumber);

  // Extract just the digits
  const digits = e164.replace(/\D/g, '');

  // Format US numbers (assuming all numbers are US)
  if (digits.length === 11 && digits.startsWith('1')) {
    const areaCode = digits.substring(1, 4);
    const prefix = digits.substring(4, 7);
    const lineNumber = digits.substring(7, 11);
    return `(${areaCode}) ${prefix}-${lineNumber}`;
  }

  // Format 10-digit numbers
  if (digits.length === 10) {
    const areaCode = digits.substring(0, 3);
    const prefix = digits.substring(3, 6);
    const lineNumber = digits.substring(6, 10);
    return `(${areaCode}) ${prefix}-${lineNumber}`;
  }

  // Return the original for other formats
  return phoneNumber.toString();
}

/**
 * Formats a phone number for API submission (just digits)
 * @param phoneNumber - The phone number to format
 * @returns Formatted phone number as a number type
 */
export function formatPhoneForApi(phoneNumber: bigint | string): number {
  // Convert to string if it's a number
  let phoneStr = phoneNumber.toString();

  // Remove all non-digit characters
  phoneStr = phoneStr.replace(/\D/g, '');

  // Remove leading 1 if present (for US numbers)
  if (phoneStr.length === 11 && phoneStr.startsWith('1')) {
    phoneStr = phoneStr.substring(1);
  }

  // Convert back to number
  return parseInt(phoneStr, 10);
}
