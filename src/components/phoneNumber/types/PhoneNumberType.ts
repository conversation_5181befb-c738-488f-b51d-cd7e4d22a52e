export type PhoneNumber = {
    id: string;
    label: string;
    number: number;
    forwardNumber: number;
    status: "active" | "inactive";
    purchaseDate: string;
    monthlyCost: number;
    autoReply: {
        enabled: boolean;
        message: string;
        responseDelay: string;
    }
}

export type PhoneNumberMetrics = {
    id: string,
    conversations: number,
    messagesSent: number,
    messagesReceived: number,
    messagesFailed: number,
    averageClientResponseTimeSeconds: number,
    cost: {
        total: number,
        monthToDate: number
    },
    reputation?: {
        score: number,
        status: string,
        lastUpdated: string
    }
}
