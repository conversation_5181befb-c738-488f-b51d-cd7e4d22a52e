'use client';

import AgentsList from "@/app/agents/_components/AgentsList";
import Sidebar from "@/app/global/Sidebar";
import {useState} from "react";
import AgentDetails from "@/app/agents/_components/AgentDetails";
import AddAgentModal from "@/app/agents/_components/AddAgentModal";

export default function AgentsPage() {
    const [selectedNumber, setSelectedNumber] = useState<string | null>(null);
    const [isAddModelOpen, setIsAddModelOpen] = useState(false);

    const handleAddAgent = (areaCode: string) => {
        console.log(`Added agent with area code: ${areaCode}`);
        setIsAddModelOpen(false);
    };

    return (
        <div className="flex h-screen">
            <Sidebar/>
            <div className="flex-1 min-h-screen">
                <div className="flex h-screen">
                    <div className="w-1/3 border-r border-gray-200 p-6 overflow-y-auto">
                        <AgentsList
                            onSelectedAgent={(number) => setSelectedNumber(number)}
                            selectedAgent={selectedNumber}
                            onAddClick={() => setIsAddModelOpen(true)}
                            listTitle="Agents"
                        />
                    </div>
                    <div className="w-2/3 p-6 overflow-y-auto">
                        {selectedNumber ? (
                            <div>
                                <AgentDetails agentId={selectedNumber}/>
                            </div>
                        ) : (
                            <div className={'flex items-center justify-center h-full text-gray-400'}>
                                Select an agent to view details
                            </div>
                        )}
                    </div>

                    <AddAgentModal
                        isOpen={isAddModelOpen}
                        onClose={() => setIsAddModelOpen(false)}
                        onAdd={handleAddAgent}
                        title="Add New Agent"
                    />
                </div>
            </div>
        </div>
    )
}
