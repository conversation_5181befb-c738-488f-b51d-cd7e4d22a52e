'use client';

import {useEffect, useState} from 'react';
import Image from 'next/image';
import {formatPhoneForDisplay} from "@/components/phoneNumber/phoneFormatters";
import { AgentStatus } from '@/../generated/prisma';
import {Agent} from "@/app/agents/types/AgentType";

interface AgentListProps {
  onSelectedAgent: (agent: string) => void;
  selectedAgent: string | null;
  onAddClick?: () => void;
  listTitle?: string;
}

export default function AgentsList({
  onSelectedAgent,
  selectedAgent,
  onAddClick,
  listTitle = "Agents"
}: AgentListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [agents, setAgents] = useState<Agent[]>([]);
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAgents = async () => {
      setIsLoading(true);
      try {
        const agents = await fetch('/api/v1/agents').then((response: Response) => response.json());
        setAgents(await agents);
        setFilteredAgents(await agents);
        console.debug(agents);
      }catch (err) {
        console.error(err);
        setError('Failed to load agents');
      }finally {
        setIsLoading(false);
      }
    }
    fetchAgents();
  },[]);

  // Filter agents when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredAgents(agents);
      return;
    }

    const normalizedSearchTerm = searchTerm.toLowerCase().trim();
    const filtered = agents.filter(agent => {
      // Search by label (if exists)
      if (agent.label && agent.label.toLowerCase().includes(normalizedSearchTerm)) {
        return true;
      }

      // Search by phone number
      if (agent.phoneNumber.number.includes(normalizedSearchTerm)) {
        return true;
      }

      // Search by formatted phone number
      const formattedNumber = formatPhoneForDisplay(agent.phoneNumber.number);
      if (formattedNumber.toLowerCase().includes(normalizedSearchTerm)) {
        return true;
      }

      return false;
    });

    setFilteredAgents(filtered);
  }, [searchTerm, agents]);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">{listTitle}</h2>
        <button
          className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg"
          onClick={onAddClick}
        >
          + Add Agent
        </button>
      </div>

      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Search agents..."
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute right-3 top-2.5">
            <Image
              src="/icons/search.svg"
              alt="Search"
              width={20}
              height={20}
              className="text-gray-400"
            />
          </div>
        </div>
      </div>

      <div className="space-y-3">
        {isLoading ? (
          <div className="text-center py-8 text-gray-500">
            Loading agents...
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-500">
            {error}
          </div>
        ) : filteredAgents.length > 0 ? (
          filteredAgents.map((agent) => (
            <div
              key={agent.id}
              className={`card p-4 cursor-pointer ${
                selectedAgent === agent.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => onSelectedAgent(agent.id)}
            >
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">{agent.label}</p>
                  <p className="font-light">{formatPhoneForDisplay(agent.phoneNumber.number)}</p>
                  <div className="flex justify-between">
                    <p className="text-sm text-gray-500">
                      Status: <span className={agent.status == AgentStatus.ACTIVE ? 'text-green-500' : 'text-red-500'}>
                        {agent.status.toLowerCase()}
                      </span>
                    </p>
                  </div>
                </div>
                <div className="text-gray-400">
                  →
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            No agents found
          </div>
        )}
      </div>
    </div>
  );
}
