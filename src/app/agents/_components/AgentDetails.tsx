'use client';

import {useEffect, useState} from 'react';
import {formatPhoneForDisplay} from "@/components/phoneNumber/phoneFormatters";
import {LoadingIndicator} from "@/app/global/loading-indicator";
import {Agent, AgentMetrics, getAutoRespondDelayDefinition} from "@/app/agents/types/AgentType";
import Link from 'next/link';

interface AgentDetailsProps {
    agentId: string;
}

export default function AgentDetails({agentId}: AgentDetailsProps) {
    const [isLoading, setIsLoading] = useState(true);
    const [agent, setAgent] = useState<Agent>();
    const [isMetricsLoading, setIsMetricsLoading] = useState(true);
    const [agentMetrics, setAgentMetrics] = useState<AgentMetrics>();
    const [isEditing, setIsEditing] = useState(false);

    const handleSave = async () => {
        if (!agent) return;

        try {
            const response = await fetch(`/api/v1/agents/${agentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    label: agent.label,
                    forwardNumberId: agent.forwardNumber?.id,
                    monthlyCost: agent.monthlyCost,
                    purchaseDate: agent.purchaseDate,
                    autoReply: agent.autoReply
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update agent');
            }

            setIsEditing(false);
        } catch (error) {
            console.error('Error updating agent:', error);
        }
    };

    useEffect(() => {
        const fetchAgent = async () => {
            try {
                setIsLoading(true);
                const response = await fetch(`/api/v1/agents/${agentId}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch agent details');
                }
                const data = await response.json();
                setAgent(data);
            } catch (error) {
                console.error('Error fetching agent details:', error);
            } finally {
                setIsLoading(false);
            }
        }

        const fetchAgentMetrics = async () => {
            try {
                setIsMetricsLoading(true);
                const response = await fetch(`/api/v1/agents/${agentId}/metrics`);
                if (!response.ok) {
                    throw new Error('Failed to fetch agent metrics');
                }
                const data = await response.json();
                setAgentMetrics(data);
            } catch (error) {
                console.error('Error fetching agent metrics:', error);
            } finally {
                setIsMetricsLoading(false);
            }
        }

        setIsEditing(false);

        fetchAgent();
        fetchAgentMetrics();
    }, [agentId]);

    if (!agent) return (
        <div>Failed to load Agent</div>
    );

    return (
        <div>
            <div className="flex justify-between items-center mb-6">
                {(!isLoading && agent) ? (
                    <div className="flex justify-between w-full">
                        <div>
                            <h2 className="text-2xl font-semibold">{agent.label ?? (agent.phoneNumber?.number ? formatPhoneForDisplay(agent.phoneNumber.number) : 'Unknown Number')}</h2>
                            <div className="text-xs text-gray-700">{agent.id}</div>
                        </div>
                        <div className="flex space-x-2">
                            <Link
                                href={`/conversations?agentId=${agentId}`}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
                            >
                                View Conversations
                            </Link>
                            <button className="bg-secondary hover:bg-gray-200 px-4 py-2 rounded-lg">
                                Delete
                            </button>
                            {isEditing ? (
                                <button
                                    className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg"
                                    onClick={handleSave}
                                >
                                    Save Changes
                                </button>
                            ) : (
                                <button
                                    className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg"
                                    onClick={() => setIsEditing(true)}
                                >
                                    Edit
                                </button>
                            )}
                        </div>
                    </div>
                ) : <LoadingIndicator />}
            </div>

            <div className="card p-6 mb-6">
                {(isLoading || !agent) ? (
                    <LoadingIndicator />
                ) : (
                    <div>
                        <div className="mb-6">
                            <h3 className="text-lg font-medium mb-4">Agent Details</h3>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm text-gray-500">Phone Number</p>
                                    <p>{formatPhoneForDisplay(agent.phoneNumber.number) ?? 'Unknown'}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Forward Number</p>
                                    <p>{agent.forwardNumber ? formatPhoneForDisplay(agent.forwardNumber.number) : 'None'}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Status</p>
                                    <p className={agent.status === "active" ? "text-green-500" : "text-red-500"}>
                                        {agent.status ? (agent.status.charAt(0).toUpperCase()+agent.status.slice(1).toLowerCase()) : 'Unknown'}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Purchased Date</p>
                                    <p>{agent.purchaseDate ? new Date(agent.purchaseDate).toLocaleDateString() : 'Unknown'}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Monthly Cost</p>
                                    <p>${agent.monthlyCost?.toFixed(2) ?? '0.00'}</p>
                                </div>
                                {agent.client && (
                                    <div>
                                        <p className="text-sm text-gray-500">Client</p>
                                        <p>{agent.client.name ?? 'Unknown'}</p>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div>
                            <h3 className="text-lg font-medium mb-4">Agent Auto-Responder Details</h3>
                            {(agent.autoReply.enabled) ? (
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <p className="text-sm text-gray-500">Status</p>
                                        <p className="text-green-500">Enabled</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500">Response Delay</p>
                                        <p className="">{getAutoRespondDelayDefinition(agent.autoReply.responseDelay)?.label}</p>
                                        <p className="text-xs text-gray-700">{getAutoRespondDelayDefinition(agent.autoReply.responseDelay)?.description}</p>
                                    </div>
                                    <div className="col-span-2">
                                        <p className="text-sm text-gray-500">Response Prompt</p>
                                        <p>{agent.autoReply.message}</p>
                                    </div>
                                </div>
                            ):(
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <p className="text-sm text-gray-500">Status</p>
                                        <p className={"text-red-500"}>Disabled</p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>

            <div className="card p-6 mb-6">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">Agent Metrics</h3>
                    {agentMetrics && agentMetrics.conversations > 0 && (
                        <Link
                            href={`/conversations?agentId=${agentId}`}
                            className="text-blue-500 hover:text-blue-600 text-sm font-medium transition-colors"
                        >
                            View All Conversations →
                        </Link>
                    )}
                </div>
                {(isMetricsLoading || !agentMetrics) ? (
                    <LoadingIndicator />
                ) : (
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <p className="text-sm text-gray-500">Conversations</p>
                            <p>{agentMetrics.conversations ?? '0'}</p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Messages</p>
                            <p>Sent: {agentMetrics.messagesSent ?? '0'}</p>
                            <p>Received: {agentMetrics.messagesReceived ?? '0'}</p>
                            <p>Failed: {agentMetrics.messagesFailed ?? '0'}</p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Average Client Response Time</p>
                            <p>{agentMetrics.averageClientResponseTimeSeconds ?? 0} seconds</p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-500">Cost</p>
                            <p>Total: ${agentMetrics.cost.total}</p>
                            <p>Month to Date: ${agentMetrics.cost.monthToDate}</p>
                        </div>
                        {agentMetrics.reputation && (
                            <div className="col-span-2">
                                <p className="text-sm text-gray-500">Reputation</p>
                                <p>Score: {agentMetrics.reputation.score}/10</p>
                                <p>Status: {agentMetrics.reputation.status}</p>
                                <p className="text-xs text-gray-600">Last Updated: {new Date(agentMetrics.reputation.lastUpdated).toLocaleString()}</p>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
