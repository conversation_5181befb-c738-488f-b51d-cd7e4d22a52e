'use client';

import { useState } from 'react';

interface AddPhoneNumberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (areaCode: string) => void;
  title?: string;
}

export default function AddAgentModal({
  isOpen, 
  onClose, 
  onAdd,
  title = "Add New Agent" 
}: AddPhoneNumberModalProps) {
  const [areaCode, setAreaCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  if (!isOpen) return null;
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Here you would call an API to purchase a number
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      onAdd(areaCode);
      onClose();
    } catch (error) {
      console.error('Error purchasing number:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-semibold mb-4">{title}</h2>
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Area Code
            </label>
            <input
              type="text"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="e.g. 415"
              value={areaCode}
              onChange={(e) => setAreaCode(e.target.value)}
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              Enter an area code to search for available numbers
            </p>
          </div>
          
          <div className="flex justify-end space-x-2">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 rounded-lg"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg"
              disabled={isLoading}
            >
              {isLoading ? 'Searching...' : 'Search Numbers'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}