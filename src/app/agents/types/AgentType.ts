import { PhoneNumberStatus, AutoRespondResponseDelay } from "../../../../generated/prisma";

// Define a simplified phone number type for use within Agent
type AgentPhoneNumber = {
  id: string;
  number: string;
  status: "active" | "inactive";
};

export type Agent = {
  id: string;
  label: string | null;
  phoneNumber: AgentPhoneNumber;
  forwardNumber?: AgentPhoneNumber | null;
  status: "active" | "inactive";
  // purchaseDate: string | null;
  // monthlyCost: number | null;
  autoReply: {
    enabled: boolean;
    message: string;
    responseDelay: string;
  };
  client?: {
    id: string;
    name: string;
  };
}

export type AgentMetrics = {
  id: string;
  conversations: number;
  messagesSent: number;
  messagesReceived: number;
  messagesFailed: number;
  averageClientResponseTimeSeconds: number;
  cost: {
    total: number;
    monthToDate: number;
  };
  reputation?: {
    score: number;
    status: string;
    lastUpdated: string;
  };
}

export type CreateAgentInput = {
  label?: string;
  phoneNumberId: string;
  clientId: string;
  forwardNumberId?: string;
  monthlyCost?: number;
  purchaseDate?: string;
};

export type UpdateAgentInput = {
  label?: string;
  forwardNumberId?: string | null;
  monthlyCost?: number;
  purchaseDate?: string | null;
  autoReply?: {
    enabled: boolean;
    message: string;
    responseDelay: string;
  };
};

// Auto Response Delay Definitions
export type AutoRespondDelayDefinition = {
  value: AutoRespondResponseDelay;
  label: string;
  description: string;
  estimatedDelayRange?: string;
};

export function getAutoRespondResponseDelayDefinitions(): AutoRespondDelayDefinition[] {
  return [
    {
      value: AutoRespondResponseDelay.IMMEDIATE,
      label: "Immediate",
      description: "Response sent immediately upon receiving the message",
      estimatedDelayRange: "0 seconds"
    },
    {
      value: AutoRespondResponseDelay.RANDOM_SECONDS,
      label: "Random Seconds",
      description: "Response sent after a random delay of 1-30 seconds",
      estimatedDelayRange: "1-30 seconds"
    },
    {
      value: AutoRespondResponseDelay.ONE_MINUTE,
      label: "1 Minute",
      description: "Response sent after exactly 1 minute",
      estimatedDelayRange: "60 seconds"
    },
    {
      value: AutoRespondResponseDelay.FIVE_MINUTES,
      label: "5 Minutes",
      description: "Response sent after exactly 5 minutes",
      estimatedDelayRange: "5 minutes"
    },
    {
      value: AutoRespondResponseDelay.TEN_MINUTES,
      label: "10 Minutes",
      description: "Response sent after exactly 10 minutes",
      estimatedDelayRange: "10 minutes"
    },
    {
      value: AutoRespondResponseDelay.THIRTY_MINUTES,
      label: "30 Minutes",
      description: "Response sent after exactly 30 minutes",
      estimatedDelayRange: "30 minutes"
    },
    {
      value: AutoRespondResponseDelay.RANDOM_MINUTES,
      label: "Random Minutes",
      description: "Response sent after a random delay of 1-15 minutes",
      estimatedDelayRange: "1-15 minutes"
    },
    {
      value: AutoRespondResponseDelay.ONE_HOUR,
      label: "1 Hour",
      description: "Response sent after exactly 1 hour",
      estimatedDelayRange: "60 minutes"
    }
  ];
}

// Helper function to get a specific delay definition by value
export function getAutoRespondDelayDefinition(delay: AutoRespondResponseDelay): AutoRespondDelayDefinition | undefined {
  return getAutoRespondResponseDelayDefinitions().find(def => def.value === delay);
}

// Helper function to get just the labels for dropdowns
export function getAutoRespondDelayOptions(): { value: AutoRespondResponseDelay; label: string }[] {
  return getAutoRespondResponseDelayDefinitions().map(def => ({
    value: def.value,
    label: def.label
  }));
}
