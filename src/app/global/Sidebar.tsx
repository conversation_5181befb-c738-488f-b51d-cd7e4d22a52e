'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { LoadingIndicator } from '@/app/global/loading-indicator';
import ClientSelect from '@/app/clients/_components/ClientSelectComponent';
import {
  HomeIcon,
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  PhoneIcon,
  Cog6ToothIcon,
  BuildingOfficeIcon,
  UsersIcon
} from '@heroicons/react/24/outline';

const CACHE_KEY = 'selectedClientId';

export default function Sidebar() {
  const pathname = usePathname();
  const [activeItem, setActiveItem] = useState('dashboard');
  const [clientId, setClientId] = useState<string | null>(() => {
    // Initialize from cache if available
    if (typeof window !== 'undefined') {
      return localStorage.getItem(CACHE_KEY);
    }
    return null;
  });

  useEffect(() => {
    // Set the active item based on the current path
    if (pathname === '/') {
      setActiveItem('dashboard');
    } else if (pathname === '/conversations') {
      setActiveItem('conversations');
    } else if (pathname === '/settings') {
      setActiveItem('settings');
    } else if (pathname?.startsWith('/agents')) {
      setActiveItem('agents');
    } else if (pathname?.startsWith('/clients')) {
      setActiveItem('clients');
    } else if (pathname?.startsWith('/companyclient')) {
      setActiveItem('companyclient');
    } else if (pathname?.startsWith('/leads')) {
      setActiveItem('leads');
    }
  }, [pathname]);

  const handleClientSelect = (newClientId: string) => {
    setClientId(newClientId);
    localStorage.setItem(CACHE_KEY, newClientId);
  };

  const navItems = [
    { id: 'dashboard', label: 'Dashboard', href: '/', icon: HomeIcon },
    { id: 'conversations', label: 'Conversations', href: '/conversations', icon: ChatBubbleLeftRightIcon },
    { id: 'agents', label: 'Agents', href: '/agents', icon: PhoneIcon },
    { id: 'leads', label: 'Leads', href: '/leads', icon: UsersIcon },
    { id: 'clients', label: 'Clients', href: '/clients', icon: UserGroupIcon },
    { id: 'settings', label: 'Settings', href: '/settings', icon: Cog6ToothIcon },
  ];

  return (
    <div className="h-screen flex flex-col bg-white border-r border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <Link href="/" className="flex items-center">
          <Image src="/logo.svg" alt="Back Talk Logo" width={32} height={32} />
          <span className="ml-2 text-xl font-semibold">Back Talk</span>
        </Link>
      </div>

      <div className="p-4 border-b border-gray-200">
        <ClientSelect
          selectedClientId={clientId}
          onClientSelect={handleClientSelect}
        />
      </div>

      <nav className="flex-1 p-4 space-y-1">
        {navItems.map((item) => (
          <Link
            key={item.id}
            href={item.href}
            className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
              activeItem === item.id
                ? 'bg-primary text-white'
                : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <item.icon className="h-5 w-5 mr-2" />
            {item.label}
          </Link>
        ))}
      </nav>
    </div>
  );
}
