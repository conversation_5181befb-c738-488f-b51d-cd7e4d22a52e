@import "tailwindcss";

:root {
  --background: #eff4f6;     /* light background */
  --foreground: #142936;     /* dark text color */
  --primary:    #268f9f;     /* main brand color */
  --primary-dark: #0b5e66;   /* darker shade of primary */
  --secondary: #d0dce3;      /* subtle background blocks */
  --border:    #d0dce3;      /* matching secondary for borders */
}


@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-secondary: var(--secondary);
  --color-border: var(--border);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/*@media (prefers-color-scheme: dark) {*/
/*  :root {*/
/*    --background: #0f172a;*/
/*    --foreground: #f8fafc;*/
/*    --primary: #2A8E9E;*/
/*    --primary-dark: #0d4d57;*/
/*    --secondary: #1e293b;*/
/*    --border: #334155;*/
/*  }*/
/*}*/

body {
  background: var(--background);
  color: var(--foreground);
}

.sidebar {
  background: white;
  border-right: 1px solid var(--border);
}

.card {
  background: white;
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/*@media (prefers-color-scheme: dark) {*/
/*  .sidebar, .card {*/
/*    background: var(--secondary);*/
/*  }*/
/*}*/

.content-wrapper {
  @apply flex;
  @apply h-screen;
}
