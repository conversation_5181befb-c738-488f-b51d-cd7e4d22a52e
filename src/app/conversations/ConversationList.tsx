'use client';

import {useEffect, useState} from 'react';
import Link from 'next/link';
import { formatPhoneForDisplay } from "@/components/phoneNumber/phoneFormatters";
import { formatDistanceToNow } from 'date-fns';
import {
  FunnelIcon,
  MagnifyingGlassIcon,
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  UserIcon,
  CheckCircleIcon,
  XMarkIcon,
  ClockIcon
} from "@heroicons/react/24/outline";
import {Conversation, ConversationFilters, MessageListProps} from "@/app/conversations/ConversationType";
import {LoadingIndicator} from "@/app/global/loading-indicator";

export default function ConversationList({
  selectedConversationId,
  clientId,
  agentId
}: MessageListProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<ConversationFilters>({});

  useEffect(() => {
    fetchConversations();
  }, [clientId, agentId, filters]);

  const fetchConversations = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (clientId) params.append('clientId', clientId);
      if (agentId) params.append('agentId', agentId);
      if (filters.status) params.append('status', filters.status);
      if (filters.isRead !== undefined) params.append('isRead', filters.isRead.toString());
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
      if (filters.dateTo) params.append('dateTo', filters.dateTo);

      const response = await fetch(`/api/v1/conversations?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch conversations');
      }
      const data = await response.json();
      setConversations(data);
    } catch (err) {
      console.error('Error fetching conversations:', err);
      setError('Failed to load conversations');
    } finally {
      setIsLoading(false);
    }
  };

  const searchConversations = (term: string) => {
    setSearchTerm(term);
    setFilters(prev => ({...prev, search: term || undefined}));
  }

  const handleFilterChange = (key: keyof ConversationFilters, value: any) => {
    setFilters(prev => ({...prev, [key]: value}))
  }

  const handleStatusChange = async (conversationId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/v1/conversations/${conversationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update conversation status');
      }

      // Refresh conversations list
      fetchConversations();
    } catch (error) {
      console.error('Error updating conversation status:', error);
      alert('Failed to update conversation status');
    }
  };

  const filteredConversations = conversations.filter(conversation => {
    const leadName = conversation.lead.name || formatPhoneForDisplay(conversation.lead.phone_number.number);
    const phoneNumber = formatPhoneForDisplay(conversation.lead.phone_number.number);
    const agentName = conversation.agent.label || formatPhoneForDisplay(conversation.agent.phone_number.number);
    const agentNumber = formatPhoneForDisplay(conversation.agent.phone_number.number);
    const messageCount = conversation.message_count;
    const lastMessageBody = conversation.last_message?.body || '';

    const searchLower = searchTerm.toLowerCase();
    return (
      leadName.toLowerCase().includes(searchLower) ||
      agentName.toLowerCase().includes(searchLower) ||
      lastMessageBody.toLowerCase().includes(searchLower)
    );
  });

  if (isLoading) {
    return (
        <div className="flex justify-center items-center h-64">
          <LoadingIndicator />
        </div>
    );
  }

  if (error) {
    return (
        <div className="p-4 text-red-500 text-center">
          {error}
        </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Conversations</h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100"
          >
            <FunnelIcon className="h-5 w-5" />
          </button>
        </div>

        {/* search */}
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"/>
          <input
              type="text"
              placeholder="Search conversations..."
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {showFilters && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Conversation Status
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={filters.isRead?.toString() || ''}
                  onChange={(e) => handleFilterChange('isRead',
                    e.target.value === '' ? undefined : e.target.value === 'true'
                  )}
                >
                  <option value="">All Conversations</option>
                  <option value="true">Read</option>
                  <option value="false">Unread</option>
                </select>
              </div>
            </div>
        )}
      </div>

      <div className="flex-1 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            {searchTerm ? 'No conversations match your search' : 'No conversations found'}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredConversations.map((conversation) => {
              const isSelected = conversation.id === selectedConversationId;
              const leadName = conversation.lead.name || formatPhoneForDisplay(conversation.lead.phone_number.number);
              const agentName = conversation.agent.label || formatPhoneForDisplay(conversation.agent.phone_number.number);
              const status = conversation.status || 'ACTIVE';

              return (
                <div
                  key={conversation.id}
                  className={`p-4 cursor-pointer hover:bg-gray-50 ${
                    isSelected ? 'bg-primary-light border-r-4 border-primary' : ''
                  }`}
                >
                  <Link
                    href={`/conversations?${agentId ? `agentId=${agentId}&` : ''}conversationId=${conversation.id}`}
                    className="block"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center mb-1">
                          <UserIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {leadName}
                          </p>
                        </div>

                        <div className="flex items-center mb-1">
                          <PhoneIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                          <p className="text-sm text-gray-600 truncate">
                            via {agentName}
                          </p>
                        </div>

                        {conversation.last_message && (
                          <div className="flex items-center mb-1">
                            <ChatBubbleLeftRightIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                            <p className="text-xs text-gray-500 truncate">
                              <span className={`font-medium ${
                                conversation.last_message.direction === 'INBOUND' ? 'text-blue-600' : 'text-green-600'
                              }`}>
                                {conversation.last_message.direction === 'INBOUND' ? 'Lead: ' : 'Agent: '}
                              </span>
                              {conversation.last_message.body}
                            </p>
                          </div>
                        )}

                        <div className="flex items-center text-xs text-gray-500">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            status === 'RESOLVED' ? 'bg-green-100 text-green-800' :
                            status === 'CLOSED' ? 'bg-gray-100 text-gray-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {status}
                          </span>
                        </div>
                      </div>

                      <div className="flex flex-col items-end ml-2">
                        <div className="flex items-center space-x-2 mb-1">
                          {conversation.unread_count > 0 && (
                            <div className="flex items-center text-xs text-red-600">
                              <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                                {conversation.unread_count}
                              </span>
                            </div>
                          )}
                        </div>

                        {conversation.last_message && (
                          <p className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(conversation.last_message.created_at), { addSuffix: true })}
                          </p>
                        )}
                      </div>
                    </div>
                  </Link>

                  {/* Status Action Buttons */}
                  <div className="mt-2 flex items-center space-x-2">
                    {status === 'ACTIVE' && (
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          handleStatusChange(conversation.id, 'RESOLVED');
                        }}
                        className="flex items-center text-xs text-green-600 hover:text-green-800 bg-green-50 hover:bg-green-100 px-2 py-1 rounded transition-colors"
                      >
                        <CheckCircleIcon className="h-3 w-3 mr-1" />
                        Resolve
                      </button>
                    )}

                    {(status === 'ACTIVE' || status === 'RESOLVED') && (
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          handleStatusChange(conversation.id, 'CLOSED');
                        }}
                        className="flex items-center text-xs text-gray-600 hover:text-gray-800 bg-gray-50 hover:bg-gray-100 px-2 py-1 rounded transition-colors"
                      >
                        <XMarkIcon className="h-3 w-3 mr-1" />
                        Close
                      </button>
                    )}

                    {(status === 'RESOLVED' || status === 'CLOSED') && (
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          handleStatusChange(conversation.id, 'ACTIVE');
                        }}
                        className="flex items-center text-xs text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 px-2 py-1 rounded transition-colors"
                      >
                        <ClockIcon className="h-3 w-3 mr-1" />
                        Reopen
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
