export interface Conversation {
    id: string;
    status?: string;
    created_at: string;
    updated_at: string;
    agent: {
        id: string;
        label: string | null;
        phone_number: {
            number: string;
        };
    };
    lead: {
        id: string;
        name: string | null;
        phone_number: {
            number: string;
        };
    };
    last_message: {
        body: string;
        created_at: string;
        direction: 'INBOUND' | 'OUTBOUND';
    } | null;
    unread_count: number;
    message_count: number;
}

export interface MessageListProps {
    conversations: Conversation[];
    selectedConversationId: string | null;
    agentId: string | null;
    clientId: string | null;
}

export interface ConversationFilters {
    clientId?: string;
    search?: string;
    status?: string;
    isRead?: boolean;
    dateFrom?: string;
    dateTo?: string;
}
