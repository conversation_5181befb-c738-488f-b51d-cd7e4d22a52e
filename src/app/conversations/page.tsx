'use client';

import {useEffect, useState} from 'react';
import {useSearchParams} from 'next/navigation';
import ConversationList from './ConversationList';
import ConversationDetails from './ConversationDetails';
import {LoadingIndicator} from "@/app/global/loading-indicator";
import {formatPhoneForDisplay} from "@/components/phoneNumber/phoneFormatters";
import Link from 'next/link';
import Sidebar from "@/app/global/Sidebar";

interface Agent {
    id: string;
    label: string | null;
    phoneNumber: {
        number: string;
    };
}

export default function ConversationsPage() {
    const searchParams = useSearchParams();
    const agentId = searchParams.get('agentId');
    const conversationId = searchParams.get('conversationId');

    const [agents, setAgents] = useState<Agent[]>([]);
    const [agent, setAgent] = useState<Agent | null>(null);
    const [conversations, setConversations] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchAgents = async () => {
            try {
                const response = await fetch('/api/v1/agents');
                if (!response.ok) {
                    throw new Error('Failed to fetch agents');
                }
                const data = await response.json();
                setAgents(data);

                if (agentId) {
                    const selectedAgent = data.find((a: Agent) => a.id === agentId);
                    setAgent(selectedAgent || null);
                }
            } catch (err) {
                console.error('Error fetching agents:', err);
                setError('Failed to load agents');
            }
        };

        fetchAgents();
    }, [agentId]);

    useEffect(() => {
        const fetchConversations = async () => {
            setIsLoading(true);
            try {
                const url = agentId
                    ? `/api/v1/conversations?agentId=${agentId}`
                    : '/api/v1/conversations';

                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error('Failed to fetch conversations');
                }
                const data = await response.json();
                setConversations(data);
            } catch (err) {
                console.error('Error fetching conversations:', err);
                setError('Failed to load conversations');
            } finally {
                setIsLoading(false);
            }
        };

        fetchConversations();
    }, [agentId]);

    return (
        <div className="flex h-screen">
            <Sidebar/>
            <div className="flex-1 min-h-screen">

                <div className="h-screen flex flex-col">
                    {/* Header */}
                    <div className="border-b border-gray-200 p-4 bg-white">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-2xl font-semibold">
                                    {agentId && agent ? (
                                        <>Conversations for {agent.label || formatPhoneForDisplay(agent.phoneNumber.number)}</>
                                    ) : (
                                        'All Conversations'
                                    )}
                                </h1>
                                {agentId && agent && (
                                    <p className="text-sm text-gray-500 mt-1">
                                        Agent: {formatPhoneForDisplay(agent.phoneNumber.number)}
                                    </p>
                                )}
                            </div>
                            <div className="flex space-x-2">
                                {agentId && (
                                    <Link
                                        href={`/agents?selected=${agentId}`}
                                        className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                                    >
                                        Back to Agent
                                    </Link>
                                )}
                                <Link
                                    href="/agents"
                                    className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors"
                                >
                                    All Agents
                                </Link>
                            </div>
                        </div>
                    </div>

                    {/* Main content */}
                    <div className="flex-1 flex overflow-hidden">
                        {/* Conversations list */}
                        <div className="w-1/3 border-r border-gray-200 overflow-y-auto bg-white">
                            {isLoading ? (
                                <div className="flex justify-center items-center h-full">
                                    <LoadingIndicator/>
                                </div>
                            ) : error ? (
                                <div className="p-4 text-red-500">{error}</div>
                            ) : (
                                <ConversationList
                                    conversations={conversations}
                                    selectedConversationId={conversationId}
                                    agentId={agentId}
                                />
                            )}
                        </div>

                        {/* Conversation details */}
                        <div className="w-2/3 overflow-y-auto bg-gray-50">
                            {conversationId ? (
                                <ConversationDetails conversationId={conversationId}/>
                            ) : (
                                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                                    <p className="text-xl">Select a conversation to view messages</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
