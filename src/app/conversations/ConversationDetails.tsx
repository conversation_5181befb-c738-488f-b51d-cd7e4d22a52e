'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { formatPhoneForDisplay } from "@/components/phoneNumber/phoneFormatters";
import { format } from 'date-fns';
import { LoadingIndicator } from "@/app/global/loading-indicator";
import { PaperAirplaneIcon } from '@heroicons/react/24/solid';
import { useCentrifugo } from '@/app/_hooks/useCentrifugo';
import CentrifugoTest from './_components/CentrifugoTest';

interface Message {
  id: string;
  body: string;
  created_at: string;
  direction: 'INBOUND' | 'OUTBOUND';
  is_read: boolean;
  status?: string;
}

interface Voicemail {
  id: string;
  call_id: string;
  recording_sid: string;
  recording_url: string;
  duration: number;
  created_at: string;
  transcription: string;
  audio_url: string | null;
  call: {
    id: string;
    from_number: string;
    to_number: string;
    call_sid: string;
    created_at: string;
  };
}

interface Conversation {
  id: string;
  status?: string;
  agent: {
    id: string;
    label: string | null;
    phone_number: {
      number: string;
    };
  };
  lead: {
    id: string;
    name: string | null;
    phone_number: {
      number: string;
    };
  };
  messages: Message[];
  voicemails: Voicemail[];
}

interface MessageData {
  id: string;
  conversation_id: string;
  body: string;
  direction: 'INBOUND' | 'OUTBOUND';
  created_at: string;
  sender?: {
    id: string;
    name?: string;
    phone_number?: string;
  };
}

interface ConversationDetailsProps {
  conversationId: string;
}

export default function ConversationDetails({ conversationId }: ConversationDetailsProps) {
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Handle real-time message updates
  const handleNewMessage = useCallback((messageData: MessageData) => {
    console.log('Received new message:', messageData);

    setConversation(prev => {
      if (!prev || messageData.conversation_id !== prev.id) return prev;

      // Check if message already exists to avoid duplicates
      const messageExists = prev.messages.some(msg => msg.id === messageData.id);
      if (messageExists) return prev;

      // Add new message to the conversation
      const newMessage: Message = {
        id: messageData.id,
        body: messageData.body,
        created_at: messageData.created_at,
        direction: messageData.direction,
        is_read: messageData.direction === 'OUTBOUND', // Mark outbound messages as read
        status: 'delivered'
      };

      return {
        ...prev,
        messages: [...prev.messages, newMessage]
      };
    });
  }, []);

  // Handle conversation status updates
  const handleConversationUpdate = useCallback((updateData: any) => {
    console.log('Received conversation update:', updateData);

    setConversation(prev => {
      if (!prev || updateData.id !== prev.id) return prev;

      return {
        ...prev,
        status: updateData.status,
      };
    });
  }, []);

  // Initialize Centrifugo connection
  const { isConnected, isSubscribed } = useCentrifugo({
    conversationId,
    userId: 'current-user-id', // TODO: Replace with actual user ID from auth context
    onMessage: handleNewMessage,
    onConversationUpdate: handleConversationUpdate,
  });

  // Debug logging for Centrifugo connection
  useEffect(() => {
    console.log('Centrifugo connection status:', { isConnected, isSubscribed, conversationId });
  }, [isConnected, isSubscribed, conversationId]);

  useEffect(() => {
    const fetchConversation = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/v1/conversations/${conversationId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch conversation');
        }
        const data = await response.json();
        setConversation(data);
      } catch (err) {
        console.error('Error fetching conversation:', err);
        setError('Failed to load conversation');
      } finally {
        setIsLoading(false);
      }
    };

    if (conversationId) {
      fetchConversation();
    }
  }, [conversationId]);

  useEffect(() => {
    // Scroll to bottom when messages change
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation?.messages]);

  // Auto-scroll when new real-time messages arrive
  useEffect(() => {
    if (conversation?.messages.length) {
      const timer = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [conversation?.messages.length]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !conversation) {
      return;
    }

    setIsSending(true);
    try {
      const response = await fetch(`/api/v1/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          body: newMessage,
          agentId: conversation.agent.id,
          leadId: conversation.lead.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      // Clear the input immediately - real-time updates will show the message
      setNewMessage('');

      // Note: No need to refresh conversation manually since Centrifugo will
      // broadcast the new message and update the UI in real-time
    } catch (err) {
      console.error('Error sending message:', err);
      alert('Failed to send message. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingIndicator />
      </div>
    );
  }

  if (error || !conversation) {
    return (
      <div className="flex items-center justify-center h-full text-red-500">
        {error || 'Conversation not found'}
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="border-b border-gray-200 p-4 bg-white">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-lg font-semibold">
              {conversation.lead.name || formatPhoneForDisplay(conversation.lead.phone_number.number)}
            </h2>
            <p className="text-sm text-gray-500">
              Agent: {conversation.agent.label || formatPhoneForDisplay(conversation.agent.phone_number.number)}
            </p>
          </div>
          <div className="text-sm text-gray-500">
            <div className="flex items-center space-x-3">
              <span>
                {conversation.messages.length} messages
                {conversation.voicemails && conversation.voicemails.length > 0 && (
                  <span className="ml-2">• {conversation.voicemails.length} voicemail{conversation.voicemails.length !== 1 ? 's' : ''}</span>
                )}
              </span>

              {/* Real-time connection status */}
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${
                  isConnected && isSubscribed ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className="text-xs">
                  {isConnected && isSubscribed ? 'Live' : 'Offline'}
                </span>
              </div>

              {/* Conversation status */}
              {conversation.status && (
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  conversation.status === 'RESOLVED' ? 'bg-green-100 text-green-800' :
                  conversation.status === 'CLOSED' ? 'bg-gray-100 text-gray-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {conversation.status}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Development Test Panel */}
      {process.env.NODE_ENV === 'development' && (
        <CentrifugoTest conversationId={conversationId} />
      )}

      {/* Voicemails Section */}
      {conversation.voicemails && conversation.voicemails.length > 0 && (
        <div className="border-b border-gray-200 bg-blue-50 p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-3">Voicemails</h3>
          <div className="space-y-3">
            {conversation.voicemails.map((voicemail) => (
              <div key={voicemail.id} className="bg-white rounded-lg p-3 border border-blue-200">
                <div className="flex justify-between items-start mb-2">
                  <div className="text-xs text-gray-500">
                    {format(new Date(voicemail.created_at), 'MMM d, yyyy h:mm a')}
                  </div>
                  <div className="text-xs text-gray-500">
                    Duration: {Math.floor(voicemail.duration / 60)}:{(voicemail.duration % 60).toString().padStart(2, '0')}
                  </div>
                </div>

                {voicemail.transcription && (
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-700 mb-1">Transcription:</p>
                    <p className="text-sm text-gray-600 italic bg-gray-50 p-2 rounded">
                      "{voicemail.transcription}"
                    </p>
                  </div>
                )}

                {voicemail.audio_url && (
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Audio:</p>
                    <audio
                      controls
                      className="w-full h-8"
                      preload="none"
                    >
                      <source src={voicemail.audio_url} type="audio/wav" />
                      <source src={voicemail.audio_url} type="audio/mpeg" />
                      Your browser does not support the audio element.
                      <a href={voicemail.audio_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        Download Audio
                      </a>
                    </audio>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {conversation.messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            No messages in this conversation
          </div>
        ) : (
          conversation.messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.direction === 'OUTBOUND' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.direction === 'OUTBOUND'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-900'
                }`}
              >
                <p className="text-sm">{message.body}</p>
                <div className="flex justify-between items-center mt-1">
                  <p className={`text-xs ${
                    message.direction === 'OUTBOUND' ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {formatMessageTime(message.created_at)}
                  </p>
                  {message.direction === 'OUTBOUND' && (
                    <span className={`text-xs ${
                      message.status === 'delivered' ? 'text-blue-100' : 'text-blue-200'
                    }`}>
                      {message.status}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <div className="border-t border-gray-200 p-4 bg-white">
        <form onSubmit={handleSendMessage} className="flex items-center">
          <input
            type="text"
            placeholder="Type a message..."
            className="flex-1 border border-gray-300 rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            disabled={isSending}
          />
          <button
            type="submit"
            className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-r-lg transition-colors disabled:bg-gray-400"
            disabled={isSending || !newMessage.trim()}
          >
            {isSending ? (
              <LoadingIndicator size="sm" />
            ) : (
              <PaperAirplaneIcon className="h-5 w-5" />
            )}
          </button>
        </form>
      </div>
    </div>
  );
}
