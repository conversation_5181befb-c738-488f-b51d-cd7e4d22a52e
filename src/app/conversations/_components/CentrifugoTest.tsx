'use client';

import { useState, useEffect } from 'react';
import { useCentrifugo } from '@/app/_hooks/useCentrifugo';

interface CentrifugoTestProps {
  conversationId: string;
}

export default function CentrifugoTest({ conversationId }: CentrifugoTestProps) {
  const [testMessage, setTestMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [receivedMessages, setReceivedMessages] = useState<any[]>([]);
  const [connectionLogs, setConnectionLogs] = useState<string[]>([]);

  // Test Centrifugo connection
  const { isConnected, isSubscribed } = useCentrifugo({
    conversationId,
    userId: 'test-user-id',
    onMessage: (message) => {
      console.log('🎉 Real-time message received:', message);
      setReceivedMessages(prev => [...prev, { ...message, timestamp: new Date().toISOString() }]);
      addLog(`📨 Received message: ${message.body}`);
    },
    onConversationUpdate: (update) => {
      console.log('🔄 Conversation update received:', update);
      addLog(`🔄 Conversation updated: ${update.status}`);
    },
  });

  const addLog = (message: string) => {
    setConnectionLogs(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    addLog(`🔌 Connection: ${isConnected ? 'Connected' : 'Disconnected'}`);
  }, [isConnected]);

  useEffect(() => {
    addLog(`📡 Subscription: ${isSubscribed ? 'Subscribed' : 'Not subscribed'}`);
  }, [isSubscribed]);

  const sendTestMessage = async () => {
    if (!testMessage.trim()) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/v1/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          body: testMessage,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send test message');
      }

      setTestMessage('');
      addLog(`✅ Test message sent: ${testMessage}`);
      console.log('Test message sent successfully');
    } catch (error) {
      console.error('Error sending test message:', error);
      addLog(`❌ Failed to send message: ${error}`);
      alert('Failed to send test message');
    } finally {
      setIsLoading(false);
    }
  };

  const testCentrifugoConnection = async () => {
    try {
      // Test token generation
      const tokenResponse = await fetch('/api/v1/centrifugo/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: 'test-user',
          conversationIds: [conversationId]
        }),
      });

      if (!tokenResponse.ok) {
        throw new Error('Failed to get Centrifugo token');
      }

      const tokenData = await tokenResponse.json();
      console.log('Centrifugo token generated:', tokenData);
      addLog(`🎫 Token generated successfully`);
      alert('Centrifugo token generated successfully! Check console for details.');
    } catch (error) {
      console.error('Error testing Centrifugo connection:', error);
      addLog(`❌ Token generation failed: ${error}`);
      alert('Failed to test Centrifugo connection');
    }
  };

  return (
    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg mb-4">
      <h3 className="text-sm font-medium text-yellow-800 mb-3">
        🧪 Centrifugo Test Panel (Development Only)
      </h3>

      {/* Connection Status */}
      <div className="mb-4 p-3 bg-white rounded border">
        <h4 className="text-sm font-medium mb-2">Connection Status</h4>
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span>WebSocket: {isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${isSubscribed ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span>Channel: {isSubscribed ? 'Subscribed' : 'Not subscribed'}</span>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        {/* Test Token Generation */}
        <div>
          <button
            onClick={testCentrifugoConnection}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm"
          >
            Test Token Generation
          </button>
          <p className="text-xs text-yellow-700 mt-1">
            Tests if Centrifugo token API is working
          </p>
        </div>

        {/* Test Message Sending */}
        <div>
          <div className="flex space-x-2">
            <input
              type="text"
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Test message..."
              className="flex-1 px-2 py-1 border border-yellow-300 rounded text-sm"
              disabled={isLoading}
            />
            <button
              onClick={sendTestMessage}
              disabled={isLoading || !testMessage.trim()}
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm disabled:bg-gray-400"
            >
              {isLoading ? 'Sending...' : 'Send Test'}
            </button>
          </div>
          <p className="text-xs text-yellow-700 mt-1">
            Tests message sending and Centrifugo broadcasting
          </p>
        </div>

        {/* Connection Info */}
        <div className="text-xs text-yellow-700">
          <p><strong>Conversation ID:</strong> {conversationId}</p>
          <p><strong>Channel:</strong> conversations:{conversationId}</p>
          <p><strong>WebSocket URL:</strong> {process.env.NEXT_PUBLIC_CENTRIFUGO_WS_URL}</p>
        </div>

        {/* Real-time Logs */}
        <div className="mt-4 p-3 bg-white rounded border">
          <h4 className="text-sm font-medium mb-2">Real-time Activity Log</h4>
          <div className="text-xs space-y-1 max-h-32 overflow-y-auto">
            {connectionLogs.length === 0 ? (
              <p className="text-gray-500">No activity yet...</p>
            ) : (
              connectionLogs.map((log, index) => (
                <div key={index} className="font-mono text-gray-700">{log}</div>
              ))
            )}
          </div>
        </div>

        {/* Received Messages */}
        {receivedMessages.length > 0 && (
          <div className="mt-4 p-3 bg-green-50 rounded border border-green-200">
            <h4 className="text-sm font-medium mb-2 text-green-800">📨 Received Messages ({receivedMessages.length})</h4>
            <div className="text-xs space-y-2 max-h-32 overflow-y-auto">
              {receivedMessages.map((msg, index) => (
                <div key={index} className="p-2 bg-white rounded border">
                  <div className="font-medium">{msg.direction}: {msg.body}</div>
                  <div className="text-gray-500">{msg.timestamp}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
