# Leads Management

This section provides comprehensive lead management functionality for the Back Talk application.

## Features

### ✅ **Lead Display & Management**
- **Lead List View** - Browse all leads with search and filtering
- **Lead Details View** - Comprehensive lead information and activity
- **Lead Editing** - Update lead name, email, and notes
- **Lead Creation** - Add new leads with phone number and client association

### ✅ **Lead Information**
- **Contact Details** - Phone number, email, name
- **Client Association** - Linked to specific clients and business types
- **Activity Tracking** - Conversations, messages, and call history
- **Notes Management** - Add and edit detailed notes about leads

### ✅ **Search & Filtering**
- **Text Search** - Search by name, phone, email, or notes
- **Conversation Filter** - Filter leads with/without conversations
- **Client Filter** - View leads for specific clients

### ✅ **Integration Points**
- **Messages** - Direct links to conversation views
- **Agents** - Shows which agents have interacted with leads
- **Calls** - Display call history and voicemail information

## Components

### **LeadsList.tsx**
- Displays paginated list of leads
- Search and filtering functionality
- Lead selection and navigation
- Activity indicators (conversations, calls)

### **LeadDetails.tsx**
- Comprehensive lead information display
- Contact details and client information
- Activity summary with statistics
- Recent conversations and calls
- Notes display

### **LeadEditModal.tsx**
- Modal form for editing lead information
- Name, email, and notes editing
- Form validation and error handling
- Read-only display of phone and client info

### **AddLeadModal.tsx**
- Modal form for creating new leads
- Phone number input with formatting
- Client selection dropdown
- Optional name, email, and notes
- Duplicate prevention

## API Endpoints

### **GET /api/v1/leads**
Fetch leads with optional filtering:
- `clientId` - Filter by client
- `search` - Text search across fields
- `hasConversations` - Filter by conversation status
- `page` & `limit` - Pagination

### **POST /api/v1/leads**
Create new lead:
```json
{
  "phone_number": "+***********",
  "client_id": "uuid",
  "name": "John Doe",
  "email": "<EMAIL>",
  "notes": "Initial contact notes"
}
```

### **GET /api/v1/leads/[id]**
Get detailed lead information including:
- Contact details
- Client and business type
- Conversation history
- Call history
- Activity statistics

### **PUT /api/v1/leads/[id]**
Update lead information:
```json
{
  "name": "Updated Name",
  "email": "<EMAIL>",
  "notes": "Updated notes"
}
```

### **DELETE /api/v1/leads/[id]**
Delete lead (restricted if has conversations/calls)

## Data Structure

### **Lead Model**
```typescript
interface Lead {
  id: string;
  phone_number_id: string;
  client_id: string;
  name?: string | null;
  email?: string | null;
  notes?: string | null;
  created_at: string;
  updated_at: string;
  phone_number: PhoneNumber;
  client: Client;
  _count?: {
    conversations: number;
    calls: number;
  };
}
```

### **Lead Statistics**
```typescript
interface LeadStats {
  totalConversations: number;
  totalMessages: number;
  totalCalls: number;
  lastContactDate?: string;
  lastMessageDirection?: 'INBOUND' | 'OUTBOUND';
}
```

## Usage Examples

### **Navigate to Leads**
```
/leads - All leads
/leads?clientId=uuid - Client-specific leads
/leads?leadId=uuid - Direct lead selection
```

### **Search Leads**
- Type in search box to filter by name, phone, email, or notes
- Use filters to show leads with/without conversations
- Results update in real-time

### **Edit Lead**
1. Select lead from list
2. Click "Edit Lead" button
3. Update name, email, or notes
4. Save changes

### **Add New Lead**
1. Click "Add Lead" button
2. Enter phone number (required)
3. Select client (required)
4. Optionally add name, email, notes
5. Submit to create

## Navigation Integration

### **Sidebar**
- Added "Leads" navigation item with UsersIcon
- Active state management for leads routes
- Positioned between Agents and Clients

### **Cross-Navigation**
- **From Messages** - View lead details from conversations
- **From Agents** - See leads associated with agents
- **From Clients** - Filter leads by client

## Security & Validation

### **Input Validation**
- Phone number formatting and validation
- Email format validation
- Required field enforcement
- Duplicate phone number prevention per client

### **Error Handling**
- API error display in modals
- Loading states during operations
- Graceful fallbacks for missing data

## Performance Considerations

### **Pagination**
- Default 20 leads per page
- Efficient database queries with limits
- Count queries for total records

### **Lazy Loading**
- Lead details loaded on selection
- Conversation and call data loaded on demand
- Optimized database includes

### **Caching**
- Client list cached during add lead flow
- Lead list refreshes on data changes
- URL state management for navigation

## Future Enhancements

### **Potential Features**
- Lead import/export functionality
- Lead assignment to specific agents
- Lead scoring and prioritization
- Bulk operations (edit, delete, assign)
- Lead activity timeline
- Email integration
- Lead source tracking
- Automated lead nurturing workflows

## File Structure

```
src/app/leads/
├── page.tsx                    # Main leads page
├── types/
│   └── LeadType.ts            # TypeScript interfaces
├── _components/
│   ├── LeadsList.tsx          # Lead list component
│   ├── LeadDetails.tsx        # Lead details component
│   ├── LeadEditModal.tsx      # Edit modal component
│   └── AddLeadModal.tsx       # Add modal component
└── README.md                  # This documentation

src/app/api/v1/leads/
├── route.ts                   # GET /leads, POST /leads
└── [id]/
    └── route.ts              # GET, PUT, DELETE /leads/[id]
```

The Leads section provides a complete lead management solution that integrates seamlessly with the existing Back Talk application architecture.
