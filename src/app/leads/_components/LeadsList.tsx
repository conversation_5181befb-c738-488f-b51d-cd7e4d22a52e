'use client';

import { useState, useEffect } from 'react';
import { Lead, LeadFilters } from '../types/LeadType';
import { LoadingIndicator } from '@/app/global/loading-indicator';
import { formatDistanceToNow } from 'date-fns';
import { 
  PhoneIcon, 
  ChatBubbleLeftRightIcon, 
  UserIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface LeadsListProps {
  onLeadSelect: (leadId: string) => void;
  selectedLeadId: string | null;
  clientId?: string | null;
}

export default function LeadsList({ onLeadSelect, selectedLeadId, clientId }: LeadsListProps) {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<LeadFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchLeads();
  }, [clientId, filters]);

  const fetchLeads = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      
      if (clientId) params.append('clientId', clientId);
      if (filters.search) params.append('search', filters.search);
      if (filters.hasConversations !== undefined) {
        params.append('hasConversations', filters.hasConversations.toString());
      }

      const response = await fetch(`/api/v1/leads?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch leads');
      }
      
      const data = await response.json();
      setLeads(data.leads);
    } catch (err) {
      console.error('Error fetching leads:', err);
      setError('Failed to load leads');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setFilters(prev => ({ ...prev, search: term || undefined }));
  };

  const handleFilterChange = (key: keyof LeadFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const filteredLeads = leads.filter(lead => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      lead.name?.toLowerCase().includes(searchLower) ||
      lead.phone_number.number.includes(searchTerm) ||
      lead.email?.toLowerCase().includes(searchLower) ||
      lead.client.name.toLowerCase().includes(searchLower)
    );
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingIndicator />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-red-500 text-center">
        {error}
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Leads</h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100"
          >
            <FunnelIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Search */}
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search leads..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Conversation Status
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={filters.hasConversations?.toString() || ''}
                onChange={(e) => handleFilterChange('hasConversations', 
                  e.target.value === '' ? undefined : e.target.value === 'true'
                )}
              >
                <option value="">All Leads</option>
                <option value="true">With Conversations</option>
                <option value="false">No Conversations</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Leads List */}
      <div className="flex-1 overflow-y-auto">
        {filteredLeads.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            {searchTerm ? 'No leads match your search' : 'No leads found'}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredLeads.map((lead) => (
              <div
                key={lead.id}
                onClick={() => onLeadSelect(lead.id)}
                className={`p-4 cursor-pointer hover:bg-gray-50 ${
                  selectedLeadId === lead.id ? 'bg-primary-light border-r-4 border-primary' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center mb-1">
                      <UserIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {lead.name || 'Unnamed Lead'}
                      </p>
                    </div>
                    
                    <div className="flex items-center mb-1">
                      <PhoneIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                      <p className="text-sm text-gray-600 truncate">
                        {lead.phone_number.number}
                      </p>
                    </div>

                    {lead.email && (
                      <p className="text-xs text-gray-500 truncate mb-1">
                        {lead.email}
                      </p>
                    )}

                    <div className="flex items-center text-xs text-gray-500">
                      <span className="mr-3">
                        {lead.client.name}
                      </span>
                      {lead.client.business_type && (
                        <span className="px-2 py-1 bg-gray-100 rounded-full">
                          {lead.client.business_type.name}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col items-end ml-2">
                    <div className="flex items-center space-x-2 mb-1">
                      {lead._count && lead._count.conversations > 0 && (
                        <div className="flex items-center text-xs text-blue-600">
                          <ChatBubbleLeftRightIcon className="h-3 w-3 mr-1" />
                          {lead._count.conversations}
                        </div>
                      )}
                      {lead._count && lead._count.calls > 0 && (
                        <div className="flex items-center text-xs text-green-600">
                          <PhoneIcon className="h-3 w-3 mr-1" />
                          {lead._count.calls}
                        </div>
                      )}
                    </div>
                    
                    <p className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(lead.updated_at), { addSuffix: true })}
                    </p>
                  </div>
                </div>

                {lead.notes && (
                  <div className="mt-2 text-xs text-gray-600 truncate">
                    {lead.notes.length > 100 ? `${lead.notes.substring(0, 100)}...` : lead.notes}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
