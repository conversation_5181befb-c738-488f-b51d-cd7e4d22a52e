'use client';

import { useState, useEffect } from 'react';
import { LeadWithStats } from '../types/LeadType';
import { LoadingIndicator } from '@/app/global/loading-indicator';
import { formatDistanceToNow, format } from 'date-fns';
import Link from 'next/link';
import {
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  PencilIcon,
  UserIcon,
  BuildingOfficeIcon,
  CalendarIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface LeadDetailsProps {
  leadId: string;
  onEdit: () => void;
}

export default function LeadDetails({ leadId, onEdit }: LeadDetailsProps) {
  const [lead, setLead] = useState<LeadWithStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (leadId) {
      fetchLead();
    }
  }, [leadId]);

  const fetchLead = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/v1/leads/${leadId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch lead');
      }
      const data = await response.json();
      setLead(data);
    } catch (err) {
      console.error('Error fetching lead:', err);
      setError('Failed to load lead details');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingIndicator />
      </div>
    );
  }

  if (error || !lead) {
    return (
      <div className="p-4 text-red-500 text-center">
        {error || 'Lead not found'}
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <UserIcon className="h-8 w-8 text-gray-400 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {lead.name || 'Unnamed Lead'}
              </h1>
              <p className="text-sm text-gray-500">
                Lead ID: {lead.id}
              </p>
            </div>
          </div>
          <button
            onClick={onEdit}
            className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors flex items-center"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit Lead
          </button>
        </div>
      </div>

      {/* Contact Information */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-lg font-semibold mb-4">Contact Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <PhoneIcon className="h-5 w-5 text-gray-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-900">Phone Number</p>
              <p className="text-sm text-gray-600">{lead.phone_number.number}</p>
            </div>
          </div>

          {lead.email && (
            <div className="flex items-center">
              <EnvelopeIcon className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Email</p>
                <p className="text-sm text-gray-600">{lead.email}</p>
              </div>
            </div>
          )}

          <div className="flex items-center">
            <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-900">Client</p>
              <p className="text-sm text-gray-600">
                {lead.client.name}
                {lead.client.business_type && (
                  <span className="ml-2 px-2 py-1 bg-gray-100 rounded-full text-xs">
                    {lead.client.business_type.name}
                  </span>
                )}
              </p>
            </div>
          </div>

          <div className="flex items-center">
            <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-900">Created</p>
              <p className="text-sm text-gray-600">
                {format(new Date(lead.created_at), 'MMM d, yyyy h:mm a')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-lg font-semibold mb-4">Activity Summary</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600 mr-2" />
              <div>
                <p className="text-2xl font-bold text-blue-600">{lead.stats.totalConversations}</p>
                <p className="text-sm text-blue-600">Conversations</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center">
              <DocumentTextIcon className="h-6 w-6 text-green-600 mr-2" />
              <div>
                <p className="text-2xl font-bold text-green-600">{lead.stats.totalMessages}</p>
                <p className="text-sm text-green-600">Messages</p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center">
              <PhoneIcon className="h-6 w-6 text-purple-600 mr-2" />
              <div>
                <p className="text-2xl font-bold text-purple-600">{lead.stats.totalCalls}</p>
                <p className="text-sm text-purple-600">Calls</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div>
              <p className="text-sm font-medium text-gray-900">Last Contact</p>
              <p className="text-sm text-gray-600">
                {lead.stats.lastContactDate
                  ? formatDistanceToNow(new Date(lead.stats.lastContactDate), { addSuffix: true })
                  : 'Never'
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Notes */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-lg font-semibold mb-4">Notes</h2>
        {lead.notes ? (
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-700 whitespace-pre-wrap">{lead.notes}</p>
          </div>
        ) : (
          <p className="text-sm text-gray-500 italic">No notes available</p>
        )}
      </div>

      {/* Recent Conversations */}
      {lead.conversations && lead.conversations.length > 0 && (
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold mb-4">Recent Conversations</h2>
          <div className="space-y-3">
            {lead.conversations.slice(0, 5).map((conversation) => (
              <div key={conversation.id} className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <ChatBubbleLeftRightIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm font-medium">
                      Agent: {conversation.agent.label || conversation.agent.phone_number}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(conversation.updated_at), { addSuffix: true })}
                  </span>
                </div>
                {conversation.lastMessage && (
                  <p className="text-sm text-gray-600 truncate">
                    <span className={`font-medium ${
                      conversation.lastMessage.direction === 'INBOUND' ? 'text-blue-600' : 'text-green-600'
                    }`}>
                      {conversation.lastMessage.direction === 'INBOUND' ? 'Lead: ' : 'Agent: '}
                    </span>
                    {conversation.lastMessage.body}
                  </p>
                )}
                <div className="mt-2">
                  <Link
                    href={`/conversations?conversationId=${conversation.id}`}
                    className="text-xs text-primary hover:text-primary-dark"
                  >
                    View Conversation →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Calls */}
      {lead.calls && lead.calls.length > 0 && (
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Recent Calls</h2>
          <div className="space-y-3">
            {lead.calls.slice(0, 5).map((call) => (
              <div key={call.id} className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <span className="text-sm font-medium">
                        {call.direction} Call
                      </span>
                      <p className="text-xs text-gray-500">
                        {call.from_number} → {call.to_number}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {format(new Date(call.started_at), 'MMM d, h:mm a')}
                    </p>
                    <p className="text-xs text-gray-500">
                      {call.duration ? `${Math.floor(call.duration / 60)}:${(call.duration % 60).toString().padStart(2, '0')}` : 'No answer'}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
