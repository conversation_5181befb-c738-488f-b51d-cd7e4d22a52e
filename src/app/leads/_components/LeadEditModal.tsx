'use client';

import { useState, useEffect } from 'react';
import { Lead, UpdateLeadInput } from '../types/LeadType';
import { LoadingIndicator } from '@/app/global/loading-indicator';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface LeadEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (leadId: string, data: UpdateLeadInput) => Promise<void>;
  lead: Lead | null;
}

export default function LeadEditModal({ isOpen, onClose, onSave, lead }: LeadEditModalProps) {
  const [formData, setFormData] = useState<UpdateLeadInput>({
    name: '',
    email: '',
    notes: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (lead && isOpen) {
      setFormData({
        name: lead.name || '',
        email: lead.email || '',
        notes: lead.notes || '',
      });
      setError(null);
    }
  }, [lead, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!lead) return;

    setIsLoading(true);
    setError(null);

    try {
      await onSave(lead.id, formData);
      onClose();
    } catch (err) {
      console.error('Error saving lead:', err);
      setError('Failed to save lead. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof UpdateLeadInput, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold">Edit Lead</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isLoading}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Lead Name */}
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Lead Name
            </label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Enter lead name"
              disabled={isLoading}
            />
          </div>

          {/* Email */}
          <div className="mb-4">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Enter email address"
              disabled={isLoading}
            />
          </div>

          {/* Notes */}
          <div className="mb-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              id="notes"
              rows={4}
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none"
              placeholder="Add notes about this lead..."
              disabled={isLoading}
            />
            <p className="text-xs text-gray-500 mt-1">
              Use this space to track important information about the lead, their needs, preferences, or any other relevant details.
            </p>
          </div>

          {/* Read-only Information */}
          {lead && (
            <div className="mb-6 p-3 bg-gray-50 rounded-md">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Lead Information</h3>
              <div className="space-y-1 text-sm text-gray-600">
                <p><span className="font-medium">Phone:</span> {lead.phone_number.number}</p>
                <p><span className="font-medium">Client:</span> {lead.client.name}</p>
                {lead.client.business_type && (
                  <p><span className="font-medium">Business Type:</span> {lead.client.business_type.name}</p>
                )}
                <p><span className="font-medium">Created:</span> {new Date(lead.created_at).toLocaleDateString()}</p>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-md transition-colors disabled:bg-gray-400 flex items-center"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <LoadingIndicator size="sm" />
                  <span className="ml-2">Saving...</span>
                </>
              ) : (
                'Save Changes'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
