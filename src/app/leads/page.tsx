'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Sidebar from '@/app/global/Sidebar';
import LeadsList from './_components/LeadsList';
import LeadDetails from './_components/LeadDetails';
import LeadEditModal from './_components/LeadEditModal';
import AddLeadModal from './_components/AddLeadModal';
import { Lead, UpdateLeadInput, CreateLeadInput } from './types/LeadType';
import { PlusIcon } from '@heroicons/react/24/outline';

export default function LeadsPage() {
  const searchParams = useSearchParams();
  const clientId = searchParams.get('clientId');
  const leadId = searchParams.get('leadId');

  const [selectedLeadId, setSelectedLeadId] = useState<string | null>(leadId);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    if (leadId) {
      setSelectedLeadId(leadId);
    }
  }, [leadId]);

  const handleLeadSelect = (leadId: string) => {
    setSelectedLeadId(leadId);
    // Update URL without page refresh
    const url = new URL(window.location.href);
    url.searchParams.set('leadId', leadId);
    window.history.replaceState({}, '', url.toString());
  };

  const handleEditLead = async () => {
    if (!selectedLeadId) return;

    try {
      // Fetch the current lead data for editing
      const response = await fetch(`/api/v1/leads/${selectedLeadId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch lead');
      }
      const leadData = await response.json();
      setSelectedLead(leadData);
      setIsEditModalOpen(true);
    } catch (error) {
      console.error('Error fetching lead for edit:', error);
      alert('Failed to load lead data for editing');
    }
  };

  const handleSaveLead = async (leadId: string, data: UpdateLeadInput) => {
    try {
      const response = await fetch(`/api/v1/leads/${leadId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update lead');
      }

      // Refresh the data
      setRefreshKey(prev => prev + 1);
      setIsEditModalOpen(false);
    } catch (error) {
      console.error('Error updating lead:', error);
      throw error; // Re-throw to let the modal handle the error
    }
  };

  const handleAddLead = async (data: CreateLeadInput) => {
    try {
      const response = await fetch('/api/v1/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create lead');
      }

      const newLead = await response.json();

      // Refresh the data and select the new lead
      setRefreshKey(prev => prev + 1);
      setSelectedLeadId(newLead.id);
      setIsAddModalOpen(false);

      // Update URL to show the new lead
      const url = new URL(window.location.href);
      url.searchParams.set('leadId', newLead.id);
      window.history.replaceState({}, '', url.toString());
    } catch (error) {
      console.error('Error creating lead:', error);
      throw error; // Re-throw to let the modal handle the error
    }
  };

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 min-h-screen">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="border-b border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold">
                  {clientId ? 'Client Leads' : 'All Leads'}
                </h1>
                {clientId && (
                  <p className="text-sm text-gray-500 mt-1">
                    Showing leads for selected client
                  </p>
                )}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Add Lead
                </button>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex overflow-hidden">
            {/* Leads List */}
            <div className="w-1/3 border-r border-gray-200 overflow-y-auto">
              <LeadsList
                key={refreshKey} // Force refresh when data changes
                onLeadSelect={handleLeadSelect}
                selectedLeadId={selectedLeadId}
                clientId={clientId}
              />
            </div>

            {/* Lead Details */}
            <div className="flex-1 overflow-y-auto">
              {selectedLeadId ? (
                <LeadDetails
                  key={`${selectedLeadId}-${refreshKey}`} // Force refresh when data changes
                  leadId={selectedLeadId}
                  onEdit={handleEditLead}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <div className="mb-4">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Select a Lead
                    </h3>
                    <p className="text-gray-500">
                      Choose a lead from the list to view their details, conversations, and activity.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Edit Modal */}
      <LeadEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={handleSaveLead}
        lead={selectedLead}
      />

      {/* Add Modal */}
      <AddLeadModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddLead}
        preselectedClientId={clientId}
      />
    </div>
  );
}
