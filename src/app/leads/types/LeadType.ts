export interface Lead {
  id: string;
  phone_number_id: string;
  client_id: string;
  name?: string | null;
  email?: string | null;
  notes?: string | null;
  created_at: string;
  updated_at: string;
  phone_number: {
    id: string;
    number: string;
    status: string;
    created_at: string;
    updated_at: string;
  };
  client: {
    id: string;
    name: string;
    business_type?: {
      id: string;
      name: string;
    } | null;
  };
  _count?: {
    conversations: number;
    calls: number;
  };
}

export interface LeadWithStats extends Lead {
  stats: {
    totalConversations: number;
    totalMessages: number;
    totalCalls: number;
    lastContactDate?: string;
    lastMessageDirection?: 'INBOUND' | 'OUTBOUND';
  };
}

export interface UpdateLeadInput {
  name?: string;
  email?: string;
  notes?: string;
}

export interface CreateLeadInput {
  phone_number: string;
  client_id: string;
  name?: string;
  email?: string;
  notes?: string;
}

export interface LeadFilters {
  clientId?: string;
  search?: string;
  hasConversations?: boolean;
  dateFrom?: string;
  dateTo?: string;
}

export interface LeadListResponse {
  leads: Lead[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
