'use client';

import { useEffect, useState } from 'react';
import { BusinessType } from '../types/ClientType';

interface BusinessTypeSelectProps {
    value: string | undefined;
    onChange: (value: string | undefined) => void;
    required?: boolean;
}

export default function BusinessTypeSelect({ value, onChange, required = false }: BusinessTypeSelectProps) {
    const [businessTypes, setBusinessTypes] = useState<BusinessType[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        async function fetchBusinessTypes() {
            try {
                setIsLoading(true);
                const response = await fetch('/api/v1/business-types');
                
                if (!response.ok) {
                    throw new Error('Failed to fetch business types');
                }
                
                const data = await response.json();
                setBusinessTypes(data);
                setError(null);
            } catch (err) {
                setError('Error loading business types. Please try again.');
                console.error('Error fetching business types:', err);
            } finally {
                setIsLoading(false);
            }
        }

        fetchBusinessTypes();
    }, []);

    if (isLoading) {
        return (
            <div className="w-full p-2 border border-gray-300 rounded-md bg-gray-50">
                Loading business types...
            </div>
        );
    }

    if (error) {
        return (
            <div className="w-full p-2 border border-red-300 rounded-md bg-red-50 text-red-700">
                {error}
            </div>
        );
    }

    return (
        <select
            className="w-full p-2 border border-gray-300 rounded-md"
            value={value || ''}
            onChange={(e) => onChange(e.target.value === '' ? undefined : e.target.value)}
            required={required}
        >
            <option value="">Select a business type</option>
            {businessTypes.map((type) => (
                <option key={type.id} value={type.id}>
                    {type.name}
                </option>
            ))}
        </select>
    );
}