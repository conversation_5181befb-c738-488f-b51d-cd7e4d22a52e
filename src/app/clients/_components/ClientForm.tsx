'use client';

import { useState } from 'react';
import { Client, CreateClientInput, UpdateClientInput, CompanySize } from '../types/ClientType';
import BusinessTypeSelect from './BusinessTypeSelect';
import CompanySizeSelect from './CompanySizeSelect';

interface ClientFormProps {
    client?: Client|null;
    onSubmit: (data: CreateClientInput | UpdateClientInput) => Promise<void>;
    isSubmitting: boolean;
}

export default function ClientForm({ client, onSubmit, isSubmitting }: ClientFormProps) {
    const [name, setName] = useState(client?.name || '');
    const [businessTypeId, setBusinessTypeId] = useState<string | undefined>(
        client?.businessType?.id
    );
    const [companySize, setCompanySize] = useState<CompanySize | undefined>(
        client?.companySize
    );
    const [isMobile, setIsMobile] = useState<boolean>(
        client?.isMobile || false
    );
    const [offersEmergency, setOffersEmergency] = useState<boolean>(
        client?.offersEmergency || false
    );

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        const data: CreateClientInput | UpdateClientInput = {
            name,
            businessTypeId,
            companySize,
            isMobile,
            offersEmergency
        };

        await onSubmit(data);
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                    Client Name
                </label>
                <input
                    type="text"
                    className="w-full p-2 border border-gray-300 rounded-md"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                />
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                    Business Type
                </label>
                <BusinessTypeSelect
                    value={businessTypeId}
                    onChange={setBusinessTypeId}
                />
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company Size
                </label>
                <CompanySizeSelect
                    value={companySize}
                    onChange={setCompanySize}
                />
            </div>

            <div className="flex items-center">
                <input
                    type="checkbox"
                    id="isMobile"
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                    checked={isMobile}
                    onChange={(e) => setIsMobile(e.target.checked)}
                />
                <label htmlFor="isMobile" className="ml-2 block text-sm text-gray-700">
                    Mobile Service (can travel to customer location)
                </label>
            </div>

            <div className="flex items-center">
                <input
                    type="checkbox"
                    id="offersEmergency"
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                    checked={offersEmergency}
                    onChange={(e) => setOffersEmergency(e.target.checked)}
                />
                <label htmlFor="offersEmergency" className="ml-2 block text-sm text-gray-700">
                    Offers Emergency Response
                </label>
            </div>

            <div className="pt-4">
                <button
                    type="submit"
                    className="w-full bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md transition duration-200"
                    disabled={isSubmitting}
                >
                    {isSubmitting ? 'Saving...' : client ? 'Update Client' : 'Create Client'}
                </button>
            </div>
        </form>
    );
}
