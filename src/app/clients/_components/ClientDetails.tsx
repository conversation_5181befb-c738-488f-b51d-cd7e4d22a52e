'use client';

import { useState } from 'react';
import { Client, CompanySizeLabels } from '../types/ClientType';
import {LoadingIndicator} from '@/app/global/loading-indicator';

interface ClientDetailsProps {
    clientId: string;
}

export default function ClientDetails({ clientId }: ClientDetailsProps) {
    const [client, setClient] = useState<Client | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Fetch client data (implementation omitted for brevity)
    // ...

    if (isLoading) {
        return <LoadingIndicator />;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (!client) {
        return <div>Client not found</div>;
    }

    return (
        <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold mb-4">{client.name}</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 className="text-lg font-medium mb-2">Business Information</h3>
                    <div className="space-y-2">
                        <div>
                            <span className="text-gray-500">Business Type:</span>
                            <span className="ml-2">{client.businessType?.name || 'Not specified'}</span>
                        </div>
                        <div>
                            <span className="text-gray-500">Company Size:</span>
                            <span className="ml-2">
                                {client.companySize ? CompanySizeLabels[client.companySize] : 'Not specified'}
                            </span>
                        </div>
                        <div>
                            <span className="text-gray-500">Mobile Service:</span>
                            <span className="ml-2">{client.isMobile ? 'Yes' : 'No'}</span>
                        </div>
                        <div>
                            <span className="text-gray-500">Emergency Response:</span>
                            <span className="ml-2">{client.offersEmergency ? 'Yes' : 'No'}</span>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-medium mb-2">Account Information</h3>
                    <div className="space-y-2">
                        <div>
                            <span className="text-gray-500">Client ID:</span>
                            <span className="ml-2 font-mono text-sm">{client.id}</span>
                        </div>
                        <div>
                            <span className="text-gray-500">Created:</span>
                            <span className="ml-2">
                                {new Date(client.createdAt).toLocaleDateString()}
                            </span>
                        </div>
                        {client.updatedAt && (
                            <div>
                                <span className="text-gray-500">Last Updated:</span>
                                <span className="ml-2">
                                    {new Date(client.updatedAt).toLocaleDateString()}
                                </span>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
