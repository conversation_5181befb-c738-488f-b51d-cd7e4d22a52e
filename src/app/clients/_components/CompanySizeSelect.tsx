'use client';

import { CompanySize, CompanySizeLabels } from '../types/ClientType';

interface CompanySizeSelectProps {
    value: CompanySize | undefined;
    onChange: (value: CompanySize | undefined) => void;
    required?: boolean;
}

export default function CompanySizeSelect({ value, onChange, required = false }: CompanySizeSelectProps) {
    return (
        <select
            className="w-full p-2 border border-gray-300 rounded-md"
            value={value || ''}
            onChange={(e) => onChange(e.target.value === '' ? undefined : e.target.value as CompanySize)}
            required={required}
        >
            <option value="">Select company size</option>
            {Object.entries(CompanySizeLabels).map(([size, label]) => (
                <option key={size} value={size}>
                    {label}
                </option>
            ))}
        </select>
    );
}