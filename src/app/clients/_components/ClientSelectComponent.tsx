'use client';

import { useState, useEffect } from 'react';
import { Client } from '@/app/clients/types/ClientType';
import { LoadingIndicator } from '@/app/global/loading-indicator';

interface ClientSelectProps {
  selectedClientId: string | null;
  onClientSelect: (clientId: string) => void;
}

export default function ClientSelect({ selectedClientId, onClientSelect }: ClientSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/v1/clients');
        const data = await response.json();
        setClients(data);
        setIsLoading(false);
      } catch (err) {
        setError('Failed to load clients');
        setIsLoading(false);
      }
    };
    fetchClients();
  }, []);

  if (isLoading) {
    return <LoadingIndicator />;
  }

  if (error) {
    return <div className="text-red-500 text-sm">{error}</div>;
  }

  console.log(clients);
  const selectedClient = clients.find(client => client.id === selectedClientId);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 text-left text-sm bg-background border border-gray-300 rounded-md shadow-sm text-foreground hover:border-primary focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary transition-colors"
      >
        <span className={selectedClient ? 'text-foreground' : 'text-foreground-muted'}>
          {selectedClient?.name || 'Select a client'}
        </span>
        <svg className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-background border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
          {clients.map((client) => (
            <button
              key={client.id}
              onClick={() => {
                onClientSelect(client.id);
                setIsOpen(false);
              }}
              className={`w-full px-3 py-2 text-left text-sm text-foreground hover:bg-primary hover:text-white transition-colors ${
                client.id === selectedClientId ? 'bg-primary-50 text-primary-700' : ''
              }`}
            >
              {client.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
