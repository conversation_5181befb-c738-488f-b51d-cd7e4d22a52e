'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Client, CompanySizeLabels } from '../types/ClientType';
import {LoadingIndicator} from '@/app/global/loading-indicator';
import {
    BuildingOffice2Icon,
    UserGroupIcon,
    TruckIcon,
    ExclamationCircleIcon
} from '@heroicons/react/24/outline';

export default function ClientsList() {
    const [clients, setClients] = useState<Client[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const router = useRouter();

    useEffect(() => {
        async function fetchClients() {
            try {
                setIsLoading(true);
                const response = await fetch('/api/v1/clients');

                if (!response.ok) {
                    throw new Error('Failed to fetch clients');
                }

                const data = await response.json();
                setClients(data);
                setError(null);
            } catch (err) {
                setError('Error loading clients. Please try again.');
                console.error('Error fetching clients:', err);
            } finally {
                setIsLoading(false);
            }
        }

        fetchClients();
    }, []);

    if (isLoading) {
        return <LoadingIndicator />;
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                {error}
            </div>
        );
    }

    if (clients.length === 0) {
        return (
            <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
                No clients found. Create your first client to get started.
            </div>
        );
    }

    return (
        <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                    <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Client
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Business Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Size
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Features
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Created
                        </th>
                        <th scope="col" className="relative px-6 py-3">
                            <span className="sr-only">Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                    {clients.map((client) => (
                        <tr
                            key={client.id}
                            className="hover:bg-gray-50 cursor-pointer"
                            onClick={() => router.push(`/clients/${client.id}`)}
                        >
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">{client.name}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500">
                                    {client.businessType?.name || 'Not specified'}
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500">
                                    {client.companySize
                                        ? CompanySizeLabels[client.companySize]
                                        : 'Not specified'}
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex space-x-2">
                                    {client.isMobile && (
                                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                            <TruckIcon className="h-3 w-3 mr-1" />
                                            Mobile
                                        </span>
                                    )}
                                    {client.offersEmergency && (
                                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                            <ExclamationCircleIcon className="h-3 w-3 mr-1" />
                                            Emergency
                                        </span>
                                    )}
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(client.createdAt).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        router.push(`/clients/${client.id}/edit`);
                                    }}
                                    className="text-primary hover:text-primary-dark"
                                >
                                    Edit
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
