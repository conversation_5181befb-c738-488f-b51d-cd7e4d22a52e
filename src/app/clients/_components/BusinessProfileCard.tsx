'use client';

import { Client, CompanySizeLabels } from '../types/ClientType';
import {
    BuildingOffice2Icon,
    UserGroupIcon,
    TruckIcon,
    ExclamationCircleIcon
} from '@heroicons/react/24/outline';

interface BusinessProfileCardProps {
    client: Client;
}

export default function BusinessProfileCard({ client }: BusinessProfileCardProps) {
    return (
        <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium mb-4 flex items-center">
                <BuildingOffice2Icon className="h-5 w-5 mr-2 text-primary" />
                Business Profile
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                    <div className="bg-secondary p-2 rounded-md mr-3">
                        <BuildingOffice2Icon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                        <p className="text-sm font-medium">Business Type</p>
                        <p className="text-gray-700">
                            {client.businessType?.name || 'Not specified'}
                        </p>
                    </div>
                </div>

                <div className="flex items-start">
                    <div className="bg-secondary p-2 rounded-md mr-3">
                        <UserGroupIcon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                        <p className="text-sm font-medium">Company Size</p>
                        <p className="text-gray-700">
                            {client.companySize
                                ? CompanySizeLabels[client.companySize]
                                : 'Not specified'}
                        </p>
                    </div>
                </div>

                <div className="flex items-start">
                    <div className={`p-2 rounded-md mr-3 ${client.isMobile ? 'bg-green-100' : 'bg-gray-100'}`}>
                        <TruckIcon className={`h-5 w-5 ${client.isMobile ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <div>
                        <p className="text-sm font-medium">Mobile Service</p>
                        <p className={`${client.isMobile ? 'text-green-600' : 'text-gray-500'}`}>
                            {client.isMobile
                                ? 'Yes - Can travel to customer location'
                                : 'No - Service at fixed location only'}
                        </p>
                    </div>
                </div>

                <div className="flex items-start">
                    <div className={`p-2 rounded-md mr-3 ${client.offersEmergency ? 'bg-red-100' : 'bg-gray-100'}`}>
                        <ExclamationCircleIcon className={`h-5 w-5 ${client.offersEmergency ? 'text-red-600' : 'text-gray-400'}`} />
                    </div>
                    <div>
                        <p className="text-sm font-medium">Emergency Response</p>
                        <p className={`${client.offersEmergency ? 'text-red-600' : 'text-gray-500'}`}>
                            {client.offersEmergency
                                ? 'Yes - Offers emergency services'
                                : 'No - Standard service hours only'}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
}
