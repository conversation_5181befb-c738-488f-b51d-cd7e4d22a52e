export type BusinessType = {
    id: string;
    name: string;
    description?: string;
};

export enum CompanySize {
    SOLO = "SOLO",           // 1 person
    MICRO = "MICRO",         // 2-10 people
    SMALL = "SMALL",         // 11-50 people
    MEDIUM = "MEDIUM",       // 51-200 people
    LARGE = "LARGE",         // 201-1000 people
    ENTERPRISE = "ENTERPRISE" // 1000+ people
}

export const CompanySizeLabels: Record<CompanySize, string> = {
    [CompanySize.SOLO]: "Solo (1 person)",
    [CompanySize.MICRO]: "Micro (2-10 people)",
    [CompanySize.SMALL]: "Small (11-50 people)",
    [CompanySize.MEDIUM]: "Medium (51-200 people)",
    [CompanySize.LARGE]: "Large (201-1000 people)",
    [CompanySize.ENTERPRISE]: "Enterprise (1000+ people)"
};

export type Client = {
    id: string;
    name: string;
    businessType?: BusinessType;
    companySize?: CompanySize;
    isMobile?: boolean;
    offersEmergency?: boolean;
    createdAt: string;
    updatedAt?: string;
};

export type CreateClientInput = {
    name: string;
    businessTypeId?: string;
    companySize?: CompanySize;
    isMobile?: boolean;
    offersEmergency?: boolean;
};

export type UpdateClientInput = {
    name?: string;
    businessTypeId?: string | null;
    companySize?: CompanySize | null;
    isMobile?: boolean;
    offersEmergency?: boolean;
};
