'use client';

import {useEffect, useState} from 'react';
import {useRouter} from 'next/navigation';
import {Client} from '../types/ClientType';
import {LoadingIndicator} from '@/app/global/loading-indicator';
import BusinessProfileCard from '../_components/BusinessProfileCard';
import Sidebar from "@/app/global/Sidebar";

export default function ClientPage({params}: { params: { id: string } }) {
    const [client, setClient] = useState<Client | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const router = useRouter();

    useEffect(() => {
        async function fetchClient() {
            try {
                setIsLoading(true);
                const response = await fetch(`/api/v1/clients/${params.id}`);

                if (!response.ok) {
                    if (response.status === 404) {
                        throw new Error('Client not found');
                    }
                    throw new Error('Failed to fetch client');
                }

                const data = await response.json();
                setClient(data);
                setError(null);
            } catch (err) {
                setError(err instanceof Error ? err.message : 'An error occurred');
                console.error('Error fetching client:', err);
            } finally {
                setIsLoading(false);
            }
        }

        fetchClient();
    }, [params.id]);

    if (isLoading) {
        return (
            <div className="p-6">
                <LoadingIndicator/>
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-6">
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                    {error}
                </div>
                <button
                    onClick={() => router.push('/clients')}
                    className="mt-4 text-primary hover:underline"
                >
                    Back to Clients
                </button>
            </div>
        );
    }

    if (!client) {
        return (
            <div className="p-6">
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
                    Client not found
                </div>
                <button
                    onClick={() => router.push('/clients')}
                    className="mt-4 text-primary hover:underline"
                >
                    Back to Clients
                </button>
            </div>
        );
    }

    return (
        <div className="flex h-screen">
            <Sidebar/>
            <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">{client.name}</h1>
                    <button
                        onClick={() => router.push(`/clients/${params.id}/edit`)}
                        className="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md transition duration-200"
                    >
                        Edit Client
                    </button>
                </div>

                <div className="space-y-6">
                    <BusinessProfileCard client={client}/>

                    {/* Other client information sections can go here */}
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h3 className="text-lg font-medium mb-4">Account Information</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p className="text-sm text-gray-500">Client ID</p>
                                <p className="font-mono text-sm">{client.id}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Created</p>
                                <p>{new Date(client.createdAt).toLocaleDateString()}</p>
                            </div>
                            {client.updatedAt && (
                                <div>
                                    <p className="text-sm text-gray-500">Last Updated</p>
                                    <p>{new Date(client.updatedAt).toLocaleDateString()}</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
