'use client';

import {useState, useEffect} from 'react';
import {useRouter, useParams} from 'next/navigation';
import {Client, UpdateClientInput} from '@/app/clients/types/ClientType';
import ClientForm from '@/app/clients/_components/ClientForm';
import {LoadingIndicator} from '@/app/global/loading-indicator';
import Sidebar from "@/app/global/Sidebar";

export default function ClientEditPage() {
    const router = useRouter();
    const params = useParams();
    const clientId = params.id as string;

    const [client, setClient] = useState<Client | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const [name, setName] = useState(client?.name || '');

    useEffect(() => {
        const fetchClient = async () => {
            try {
                const response = await fetch(`/api/v1/clients/${clientId}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch client');
                }
                const data = await response.json();
                setClient(data);
            } catch (err) {
                setError('Failed to load client data');
                console.error('Error fetching client:', err);
            } finally {
                setIsLoading(false);
            }
        };

        if (clientId) {
            fetchClient();
        }
    }, [clientId]);

    const handleSubmit = async (data: UpdateClientInput) => {
        setIsSubmitting(true);
        setError(null);

        try {
            const response = await fetch(`/api/v1/clients/${clientId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) {
                throw new Error('Failed to update client');
            }

            // Redirect back to client details page
            router.push(`/clients/${clientId}`);
        } catch (err) {
            setError('Failed to update client. Please try again.');
            console.error('Error updating client:', err);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        router.push(`/clients/${clientId}`);
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <LoadingIndicator/>
            </div>
        );
    }

    if (error && !client) {
        return (
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="max-w-2xl mx-auto px-4">
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="text-center">
                            <div className="text-red-500 text-lg mb-4">{error}</div>
                            <button
                                onClick={() => router.push('/clients')}
                                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition duration-200"
                            >
                                Back to Clients
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // return (
    //     <div className="flex h-screen">
    //         <Sidebar />
    //         <div className="flex-1 min-h-screen">
    //             <div className="card p-6 mb-6">
    //                 <h3 className="text-lg font-medium mb-4">{client?.name}</h3>
    //                 <div className="px-4">
    //                     <ClientForm
    //                         client={client}
    //                         onSubmit={handleSubmit}
    //                         isSubmitting={isSubmitting}
    //                     />
    //                 </div>
    //             </div>
    //         </div>
    //     </div>
    // );

    return (
        <div className="flex h-screen">
            <Sidebar/>
            <div className="flex-1 min-h-screen">
            <div className="min-h-screen py-8">
                <div className="mx-auto px-4">
                    <div className="rounded-lg shadow-md p-6">
                        <div className="mb-6">
                            <h1 className="text-2xl font-bold text-gray-900 mb-2">
                                Edit Client
                            </h1>
                            <p className="text-gray-600">
                                Update the information for {client?.name}
                            </p>
                        </div>

                        {error && (
                            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                                <div className="text-red-800 text-sm">{error}</div>
                            </div>
                        )}

                        {client && (
                            <ClientForm
                                client={client}
                                onSubmit={handleSubmit}
                                isSubmitting={isSubmitting}
                            />
                        )}

                        <div className="mt-6 pt-4 border-t border-gray-200">
                            <button
                                onClick={handleCancel}
                                className="w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition duration-200"
                                disabled={isSubmitting}
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    );
}
