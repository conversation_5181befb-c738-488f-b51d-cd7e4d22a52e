'use client';

import {useState} from 'react';
import {useRouter} from 'next/navigation';
import ClientsList from './_components/ClientsList';
import {PlusIcon} from '@heroicons/react/24/outline';
import Sidebar from "@/app/global/Sidebar";

export default function ClientsPage() {
    const router = useRouter();

    return (
        <div className="flex h-screen">
            <Sidebar/>
            <div className="flex-1 min-h-screen">
                <div className="p-6">
                    <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold">Clients</h1>
                        <button
                            onClick={() => router.push('/clients/new')}
                            className="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md transition duration-200 flex items-center"
                        >
                            <PlusIcon className="h-5 w-5 mr-1"/>
                            Add Client
                        </button>
                    </div>

                    <ClientsList/>
                </div>
            </div>
        </div>
    );
}
