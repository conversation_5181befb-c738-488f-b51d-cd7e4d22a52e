import { NextResponse } from "next/server";
import { PrismaClient } from '@/../generated/prisma';

const prisma = new PrismaClient();

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    const leadId = searchParams.get('leadId');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build where clause
    const where: any = {
      deleted_at: null
    };

    if (agentId) {
      where.agent_id = agentId;
    }

    if (leadId) {
      where.lead_id = leadId;
    }

    if (status) {
      where.status = status;
    }

    // Fetch conversations with related data
    const conversations = await prisma.conversation.findMany({
      where,
      include: {
        agent: {
          include: {
            phone_number: true
          }
        },
        lead: {
          include: {
            phone_number: true
          }
        },
        _count: {
          select: {
            messages: true
          }
        }
      },
      orderBy: {
        updated_at: 'desc'
      },
      take: limit,
      skip: offset
    });

    // Get the last message for each conversation and convert to plain objects
    const conversationsWithLastMessage = await Promise.all(
      conversations.map(async (conversation) => {
        const lastMessage = await prisma.message.findFirst({
          where: {
            conversation_id: conversation.id
          },
          orderBy: {
            created_at: 'desc'
          },
          select: {
            body: true,
            created_at: true,
            direction: true
          }
        });

        // Convert to plain object for JSON serialization
        return {
          id: conversation.id,
          agent_id: conversation.agent_id,
          lead_id: conversation.lead_id,
          client_id: conversation.client_id,
          carrier_conversation_id: conversation.carrier_conversation_id,
          status: conversation.status,
          created_at: conversation.created_at.toISOString(),
          updated_at: conversation.updated_at.toISOString(),
          deleted_at: conversation.deleted_at?.toISOString() || null,
          agent: {
            id: conversation.agent.id,
            label: conversation.agent.label,
            status: conversation.agent.status,
            phone_number_id: conversation.agent.phone_number_id,
            client_id: conversation.agent.client_id,
            forward_number_id: conversation.agent.forward_number_id,
            created_at: conversation.agent.created_at.toISOString(),
            updated_at: conversation.agent.updated_at.toISOString(),
            phone_number: {
              id: conversation.agent.phone_number.id,
              number: conversation.agent.phone_number.number.toString(),
              status: conversation.agent.phone_number.status,
              monthly_cost: conversation.agent.phone_number.monthly_cost?.toString() || null,
              purchase_date: conversation.agent.phone_number.purchase_date?.toISOString() || null,
              created_at: conversation.agent.phone_number.created_at.toISOString(),
              updated_at: conversation.agent.phone_number.updated_at.toISOString()
            }
          },
          lead: {
            id: conversation.lead.id,
            phone_number_id: conversation.lead.phone_number_id,
            client_id: conversation.lead.client_id,
            name: conversation.lead.name,
            email: conversation.lead.email,
            notes: conversation.lead.notes,
            created_at: conversation.lead.created_at.toISOString(),
            updated_at: conversation.lead.updated_at.toISOString(),
            phone_number: {
              id: conversation.lead.phone_number.id,
              number: conversation.lead.phone_number.number.toString(),
              status: conversation.lead.phone_number.status,
              monthly_cost: conversation.lead.phone_number.monthly_cost?.toString() || null,
              purchase_date: conversation.lead.phone_number.purchase_date?.toISOString() || null,
              created_at: conversation.lead.phone_number.created_at.toISOString(),
              updated_at: conversation.lead.phone_number.updated_at.toISOString()
            }
          },
          _count: conversation._count,
          lastMessage: lastMessage ? {
            body: lastMessage.body,
            created_at: lastMessage.created_at.toISOString(),
            direction: lastMessage.direction
          } : null
        };
      })
    );

    return NextResponse.json(conversationsWithLastMessage);
  } catch (error) {
    console.error('Error fetching conversations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch conversations' },
      { status: 500 }
    );
  }
}
