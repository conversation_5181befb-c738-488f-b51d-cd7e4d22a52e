import { NextResponse } from 'next/server';
import { toggleConversationAgent, getConversationWithAgentStatus } from '../../../_components/conversation/conversation-service';

/**
 * GET /api/v1/conversations/[id]/agent
 * Get conversation agent status
 */
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
    
    const conversation = await getConversationWithAgentStatus(id);
    
    if (!conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      conversationId: conversation.id,
      isAgentActive: conversation.is_agent_active,
      status: conversation.status,
    });

  } catch (error) {
    console.error('Error fetching conversation agent status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch conversation agent status' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/v1/conversations/[id]/agent
 * Toggle conversation agent active status
 */
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { isActive } = body;

    if (typeof isActive !== 'boolean') {
      return NextResponse.json(
        { error: 'isActive must be a boolean value' },
        { status: 400 }
      );
    }

    const updatedConversation = await toggleConversationAgent(id, isActive);

    return NextResponse.json({
      conversationId: updatedConversation.id,
      isAgentActive: updatedConversation.is_agent_active,
      status: updatedConversation.status,
      message: `Agent ${isActive ? 'enabled' : 'disabled'} for conversation`,
    });

  } catch (error) {
    console.error('Error toggling conversation agent:', error);
    return NextResponse.json(
      { error: 'Failed to toggle conversation agent' },
      { status: 500 }
    );
  }
}
