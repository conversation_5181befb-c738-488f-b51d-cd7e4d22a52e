import { NextResponse } from "next/server";
import { PrismaClient } from '@/../generated/prisma';
import twilio from 'twilio';
import { broadcastMessage } from "@/app/api/v1/_components/centrifugo";
import { createPersonMessage, shouldSendAutomatedResponse } from "../../_components/conversation/conversation-service";

const prisma = new PrismaClient();

// Initialize Twilio client
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { body, agentId, personId } = await request.json();

    // Validate input
    if (!body || !body.trim()) {
      return NextResponse.json(
        { error: "Message body is required" },
        { status: 400 }
      );
    }

    // Validate that either agentId or personId is provided, but not both
    if (agentId && personId) {
      return NextResponse.json(
        { error: "Message cannot be created by both agent and person" },
        { status: 400 }
      );
    }

    if (!agentId && !personId) {
      return NextResponse.json(
        { error: "Either agentId or personId must be provided" },
        { status: 400 }
      );
    }

    // Get conversation details
    const conversation = await prisma.conversation.findUnique({
      where: { id },
      include: {
        agent: {
          include: {
            phone_number: true
          }
        },
        lead: {
          include: {
            phone_number: true,
            person: true
          }
        }
      }
    });

    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    let message;

    if (personId) {
      // This is a manual message from a person (user interface)
      // Use the conversation service which will automatically disable the agent
      message = await createPersonMessage({
        conversationId: id,
        body,
        phoneNumberId: conversation.agent.phone_number_id,
        clientId: conversation.client_id,
        personId: personId,
        direction: 'OUTBOUND',
      });

      // Send the message via Twilio
      const from = conversation.agent.phone_number.number.toString();
      const to = conversation.lead.phone_number.number.toString();

      try {
        const twilioResponse = await twilioClient.messages.create({
          body,
          from: from,
          to: to,
        });

        // Update message status with Twilio SID
        await prisma.message.update({
          where: { id: message.id },
          data: { status: 'sent' }
        });
      } catch (twilioError) {
        console.error('Failed to send message via Twilio:', twilioError);
        // Update message status to failed
        await prisma.message.update({
          where: { id: message.id },
          data: { status: 'failed' }
        });
        throw twilioError;
      }
    } else {
      // This is an automated agent message
      // Send the message via Twilio first
      const from = conversation.agent.phone_number.number.toString();
      const to = conversation.lead.phone_number.number.toString();

      const twilioResponse = await twilioClient.messages.create({
        body,
        from: from,
        to: to,
      });

      // Store the outgoing message with agent creator
      message = await prisma.message.create({
        data: {
          conversation_id: id,
          client_id: conversation.client_id,
          phone_number_id: conversation.agent.phone_number_id,
          body,
          status: 'sent',
          direction: 'OUTBOUND',
          is_read: true,
          created_by_agent: agentId,
          cost: 0.0 // You might want to calculate this based on Twilio's pricing
        },
        include: {
          agent: true,
          person: true,
        }
      });
    }

    // Broadcast the message to Centrifugo for real-time updates
    try {
      const senderInfo = personId ? {
        id: conversation.lead.person?.id || 'unknown',
        name: conversation.lead.person?.name || 'User',
        phone_number: conversation.lead.phone_number.number.toString(),
      } : {
        id: conversation.agent.id,
        name: conversation.agent.label || 'Agent',
        phone_number: conversation.agent.phone_number.number.toString(),
      };

      await broadcastMessage({
        conversationId: id,
        message: {
          id: message.id,
          conversation_id: id,
          body: body,
          direction: 'OUTBOUND',
          created_at: message.created_at.toISOString(),
          sender: senderInfo,
          created_by_agent: message.created_by_agent,
          created_by_person: message.created_by_person,
        }
      });
    } catch (centrifugoError) {
      console.error('Failed to broadcast message to Centrifugo:', centrifugoError);
      // Continue processing even if Centrifugo broadcast fails
    }

    return NextResponse.json({
      id: message.id,
      conversation_id: message.conversation_id,
      body: message.body,
      direction: message.direction,
      created_at: message.created_at.toISOString(),
      status: message.status,
      is_read: message.is_read,
      created_by_agent: message.created_by_agent,
      created_by_person: message.created_by_person,
      agent_disabled: personId ? true : false, // Indicate if agent was disabled by this message
    });
  } catch (error) {
    console.error("Error sending message:", error);
    return NextResponse.json(
      { error: "Failed to send message" },
      { status: 500 }
    );
  }
}
