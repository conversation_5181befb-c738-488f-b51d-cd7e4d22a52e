import { NextResponse } from "next/server";
import { PrismaClient } from '@/../generated/prisma';
import twilio from 'twilio';
import { broadcastMessage } from "@/app/api/v1/_components/centrifugo";

const prisma = new PrismaClient();

// Initialize Twilio client
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { body, agentId } = await request.json();

    // Validate input
    if (!body || !body.trim()) {
      return NextResponse.json(
        { error: "Message body is required" },
        { status: 400 }
      );
    }

    // Get conversation details
    const conversation = await prisma.conversation.findUnique({
      where: { id },
      include: {
        agent: {
          include: {
            phone_number: true
          }
        },
        lead: {
          include: {
            phone_number: true
          }
        }
      }
    });

    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    // Send the message via Twilio
    const from = conversation.agent.phone_number.number.toString();
    const to = conversation.lead.phone_number.number.toString();

    const twilioResponse = await twilioClient.messages.create({
      body,
      from: from,
      to: to,
    });

    // Store the outgoing message
    const message = await prisma.message.create({
      data: {
        conversation_id: id,
        client_id: conversation.client_id,
        phone_number_id: conversation.agent.phone_number_id,
        body,
        status: 'sent',
        direction: 'OUTBOUND',
        is_read: true,
        cost: 0.0 // You might want to calculate this based on Twilio's pricing
      }
    });

    // Broadcast the message to Centrifugo for real-time updates
    try {
      await broadcastMessage({
        conversationId: id,
        message: {
          id: message.id,
          conversation_id: id,
          body: body,
          direction: 'OUTBOUND',
          created_at: message.created_at.toISOString(),
          sender: {
            id: conversation.agent.id,
            name: conversation.agent.label || undefined,
            phone_number: from,
          }
        }
      });
    } catch (centrifugoError) {
      console.error('Failed to broadcast message to Centrifugo:', centrifugoError);
      // Continue processing even if Centrifugo broadcast fails
    }

    return NextResponse.json({
      id: message.id,
      conversation_id: message.conversation_id,
      body: message.body,
      direction: message.direction,
      created_at: message.created_at.toISOString(),
      status: message.status,
      is_read: message.is_read
    });
  } catch (error) {
    console.error("Error sending message:", error);
    return NextResponse.json(
      { error: "Failed to send message" },
      { status: 500 }
    );
  }
}
