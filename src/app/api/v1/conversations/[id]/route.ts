import { NextResponse } from "next/server";
import { PrismaClient } from '@/../generated/prisma';
import { generatePresignedUrl } from "../../_components/s3";
import { broadcastConversationUpdate } from "../../_components/centrifugo";

const prisma = new PrismaClient();

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;

    const conversation = await prisma.conversation.findUnique({
      where: {
        id: id
      },
      include: {
        agent: {
          include: {
            phone_number: true,
            client: true
          }
        },
        lead: {
          include: {
            phone_number: true,
            person: true
          }
        },
        messages: {
          include: {
            agent: true,
            person: true
          },
          orderBy: {
            created_at: 'asc'
          }
        }
      }
    });

    // Find any voicemails associated with this conversation's lead
    const voicemails = await prisma.voicemail.findMany({
      where: {
        call: {
          lead_id: conversation?.lead_id,
          agent_id: conversation?.agent_id
        }
      },
      include: {
        call: true
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    if (!conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    // Convert to plain object for JSON serialization
    const conversationResponse = {
      id: conversation.id,
      agent_id: conversation.agent_id,
      lead_id: conversation.lead_id,
      client_id: conversation.client_id,
      carrier_conversation_id: conversation.carrier_conversation_id,
      status: conversation.status,
      is_agent_active: conversation.is_agent_active,
      created_at: conversation.created_at.toISOString(),
      updated_at: conversation.updated_at?.toISOString(),
      deleted_at: conversation.deleted_at?.toISOString() || null,
      agent: {
        id: conversation.agent.id,
        label: conversation.agent.label,
        status: conversation.agent.status,
        phone_number_id: conversation.agent.phone_number_id,
        client_id: conversation.agent.client_id,
        forward_number_id: conversation.agent.forward_number_id,
        created_at: conversation.agent.created_at.toISOString(),
        updated_at: conversation.agent.updated_at?.toISOString(),
        phone_number: {
          id: conversation.agent.phone_number.id,
          number: conversation.agent.phone_number.number.toString(),
          status: conversation.agent.phone_number.status,
          monthly_cost: conversation.agent.phone_number.monthly_cost?.toString() || null,
          purchase_date: conversation.agent.phone_number.purchase_date?.toISOString() || null,
          created_at: conversation.agent.phone_number.created_at.toISOString(),
          updated_at: conversation.agent.phone_number.updated_at?.toISOString()
        }
      },
      lead: {
        id: conversation.lead.id,
        phone_number_id: conversation.lead.phone_number_id,
        client_id: conversation.lead.client_id,
        person_id: conversation.lead.person_id,
        notes: conversation.lead.notes,
        created_at: conversation.lead.created_at.toISOString(),
        updated_at: conversation.lead.updated_at?.toISOString(),
        person: conversation.lead.person ? {
          id: conversation.lead.person.id,
          name: conversation.lead.person.name,
          email: conversation.lead.person.email,
          created_at: conversation.lead.person.created_at.toISOString(),
          updated_at: conversation.lead.person.updated_at.toISOString()
        } : null,
        phone_number: {
          id: conversation.lead.phone_number.id,
          number: conversation.lead.phone_number.number.toString(),
          status: conversation.lead.phone_number.status,
          monthly_cost: conversation.lead.phone_number.monthly_cost?.toString() || null,
          purchase_date: conversation.lead.phone_number.purchase_date?.toISOString() || null,
          created_at: conversation.lead.phone_number.created_at.toISOString(),
          updated_at: conversation.lead.phone_number.updated_at?.toISOString()
        }
      },
      messages: conversation.messages.map(message => ({
        id: message.id,
        conversation_id: message.conversation_id,
        client_id: message.client_id,
        phone_number_id: message.phone_number_id,
        body: message.body,
        created_at: message.created_at.toISOString(),
        status: message.status,
        direction: message.direction,
        is_read: message.is_read,
        generation_id: message.generation_id,
        cost: message.cost.toString(),
        created_by_agent: message.created_by_agent,
        created_by_person: message.created_by_person,
        agent: message.agent ? {
          id: message.agent.id,
          label: message.agent.label
        } : null,
        person: message.person ? {
          id: message.person.id,
          name: message.person.name,
          email: message.person.email
        } : null
      })),
      voicemails: await Promise.all(voicemails.map(async (voicemail) => {
        let audioUrl = null;
        try {
          // Generate presigned URL for audio playback
          audioUrl = await generatePresignedUrl({
            clientId: conversation.agent.client_id,
            agentId: conversation.agent_id,
            voicemailId: voicemail.id,
            expiresIn: 3600 // 1 hour
          });
        } catch (error) {
          console.error('Failed to generate presigned URL for voicemail:', voicemail.id, error);
        }

        return {
          id: voicemail.id,
          call_id: voicemail.call_id,
          recording_sid: voicemail.recording_sid,
          recording_url: voicemail.recording_url,
          duration: voicemail.duration,
          created_at: voicemail.created_at.toISOString(),
          transcription: voicemail.transcription,
          audio_url: audioUrl, // Presigned URL for audio playback
          call: {
            id: voicemail.call.id,
            from_number: voicemail.call.from_number,
            to_number: voicemail.call.to_number,
            call_sid: voicemail.call.call_sid,
            created_at: voicemail.call.created_at.toISOString()
          }
        };
      }))
    };

    return NextResponse.json(conversationResponse);
  } catch (error) {
    console.error('Error fetching conversation:', error);
    return NextResponse.json(
      { error: 'Failed to fetch conversation' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { status } = body;

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Valid statuses: ACTIVE, RESOLVED, CLOSED
    const validStatuses = ['ACTIVE', 'RESOLVED', 'CLOSED'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: ACTIVE, RESOLVED, CLOSED' },
        { status: 400 }
      );
    }

    const updatedConversation = await prisma.conversation.update({
      where: { id },
      data: {
        status,
        updated_at: new Date(),
      },
      include: {
        agent: {
          include: {
            phone_number: true
          }
        },
        lead: {
          include: {
            phone_number: true
          }
        },
        messages: {
          orderBy: {
            created_at: 'desc'
          },
          take: 1
        }
      }
    });

    // Broadcast conversation update to Centrifugo
    try {
      await broadcastConversationUpdate({
        conversationId: id,
        update: {
          id: updatedConversation.id,
          status: updatedConversation.status || 'ACTIVE',
          updated_at: updatedConversation.updated_at.toISOString(),
          last_message: updatedConversation.messages[0] ? {
            id: updatedConversation.messages[0].id,
            conversation_id: updatedConversation.messages[0].conversation_id,
            body: updatedConversation.messages[0].body || '',
            direction: updatedConversation.messages[0].direction,
            created_at: updatedConversation.messages[0].created_at.toISOString(),
          } : undefined,
        }
      });
    } catch (centrifugoError) {
      console.error('Failed to broadcast conversation update to Centrifugo:', centrifugoError);
      // Continue processing even if Centrifugo broadcast fails
    }

    const conversationData = {
      id: updatedConversation.id,
      agent_id: updatedConversation.agent_id,
      lead_id: updatedConversation.lead_id,
      client_id: updatedConversation.client_id,
      status: updatedConversation.status,
      created_at: updatedConversation.created_at.toISOString(),
      updated_at: updatedConversation.updated_at.toISOString(),
      agent: {
        id: updatedConversation.agent.id,
        label: updatedConversation.agent.label,
        phone_number: {
          number: updatedConversation.agent.phone_number.number.toString()
        }
      },
      lead: {
        id: updatedConversation.lead.id,
        name: updatedConversation.lead.name,
        phone_number: {
          number: updatedConversation.lead.phone_number.number.toString()
        }
      }
    };

    return NextResponse.json(conversationData);

  } catch (error) {
    console.error('Error updating conversation:', error);

    if (error instanceof Error && error.message.includes('Record to update not found')) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update conversation' },
      { status: 500 }
    );
  }
}
