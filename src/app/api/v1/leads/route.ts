import { NextResponse } from "next/server";
import { PrismaClient } from '@/../generated/prisma';
import { formatToE164 } from "@/components/phoneNumber/phoneFormatters";

const prisma = new PrismaClient();

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get('clientId');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const hasConversations = searchParams.get('hasConversations');
    
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (clientId) {
      where.client_id = clientId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (hasConversations === 'true') {
      where.conversations = {
        some: {}
      };
    } else if (hasConversations === 'false') {
      where.conversations = {
        none: {}
      };
    }

    // Get total count
    const total = await prisma.lead.count({ where });

    // Get leads with related data
    const leads = await prisma.lead.findMany({
      where,
      include: {
        phone_number: true,
        client: {
          include: {
            business_type: true
          }
        },
        _count: {
          select: {
            conversations: true,
            calls: true
          }
        }
      },
      orderBy: {
        updated_at: 'desc'
      },
      skip,
      take: limit,
    });

    // Format the response
    const formattedLeads = leads.map(lead => ({
      id: lead.id,
      phone_number_id: lead.phone_number_id,
      client_id: lead.client_id,
      name: lead.name,
      email: lead.email,
      notes: lead.notes,
      created_at: lead.created_at.toISOString(),
      updated_at: lead.updated_at.toISOString(),
      phone_number: {
        id: lead.phone_number.id,
        number: formatToE164(lead.phone_number.number),
        status: lead.phone_number.status,
        created_at: lead.phone_number.created_at.toISOString(),
        updated_at: lead.phone_number.updated_at.toISOString(),
      },
      client: {
        id: lead.client.id,
        name: lead.client.name || 'Unknown Client',
        business_type: lead.client.business_type ? {
          id: lead.client.business_type.id,
          name: lead.client.business_type.name,
        } : null,
      },
      _count: lead._count,
    }));

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      leads: formattedLeads,
      total,
      page,
      limit,
      totalPages,
    });

  } catch (error) {
    console.error('Error fetching leads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch leads' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { phone_number, client_id, name, email, notes } = body;

    if (!phone_number || !client_id) {
      return NextResponse.json(
        { error: 'Phone number and client ID are required' },
        { status: 400 }
      );
    }

    // Clean and normalize phone number
    const cleanPhoneNumber = phone_number.replace(/\D/g, '');
    const withoutCountryCode = cleanPhoneNumber.replace(/^1/, '');
    const phoneNumberBigInt = BigInt(withoutCountryCode);

    // Check if phone number already exists for this client
    const existingLead = await prisma.lead.findFirst({
      where: {
        phone_number: {
          number: phoneNumberBigInt
        },
        client_id: client_id
      }
    });

    if (existingLead) {
      return NextResponse.json(
        { error: 'A lead with this phone number already exists for this client' },
        { status: 409 }
      );
    }

    // Find or create phone number
    let leadPhoneNumber = await prisma.phone_number.findFirst({
      where: {
        number: phoneNumberBigInt
      }
    });

    if (!leadPhoneNumber) {
      leadPhoneNumber = await prisma.phone_number.create({
        data: {
          number: phoneNumberBigInt,
          status: 'ACTIVE'
        }
      });
    }

    // Create the lead
    const newLead = await prisma.lead.create({
      data: {
        phone_number_id: leadPhoneNumber.id,
        client_id: client_id,
        name: name || formatToE164(phoneNumberBigInt),
        email: email || null,
        notes: notes || null,
      },
      include: {
        phone_number: true,
        client: {
          include: {
            business_type: true
          }
        },
        _count: {
          select: {
            conversations: true,
            calls: true
          }
        }
      }
    });

    // Format the response
    const formattedLead = {
      id: newLead.id,
      phone_number_id: newLead.phone_number_id,
      client_id: newLead.client_id,
      name: newLead.name,
      email: newLead.email,
      notes: newLead.notes,
      created_at: newLead.created_at.toISOString(),
      updated_at: newLead.updated_at.toISOString(),
      phone_number: {
        id: newLead.phone_number.id,
        number: formatToE164(newLead.phone_number.number),
        status: newLead.phone_number.status,
        created_at: newLead.phone_number.created_at.toISOString(),
        updated_at: newLead.phone_number.updated_at.toISOString(),
      },
      client: {
        id: newLead.client.id,
        name: newLead.client.name || 'Unknown Client',
        business_type: newLead.client.business_type ? {
          id: newLead.client.business_type.id,
          name: newLead.client.business_type.name,
        } : null,
      },
      _count: newLead._count,
    };

    return NextResponse.json(formattedLead, { status: 201 });

  } catch (error) {
    console.error('Error creating lead:', error);
    return NextResponse.json(
      { error: 'Failed to create lead' },
      { status: 500 }
    );
  }
}
