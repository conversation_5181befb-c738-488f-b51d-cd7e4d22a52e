import { NextResponse } from "next/server";
import { PrismaClient } from '@/../generated/prisma';
import { formatToE164 } from "@/components/phoneNumber/phoneFormatters";
import { updatePerson } from '../../_components/person/person-service';

const prisma = new PrismaClient();

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;

    const lead = await prisma.lead.findUnique({
      where: { id },
      include: {
        phone_number: true,
        client: {
          include: {
            business_type: true
          }
        },
        conversations: {
          include: {
            messages: {
              orderBy: {
                created_at: 'desc'
              },
              take: 1
            },
            agent: {
              include: {
                phone_number: true
              }
            }
          },
          orderBy: {
            updated_at: 'desc'
          }
        },
        calls: {
          orderBy: {
            started_at: 'desc'
          },
          take: 10
        },
        _count: {
          select: {
            conversations: true,
            calls: true
          }
        }
      }
    });

    if (!lead) {
      return NextResponse.json(
        { error: 'Lead not found' },
        { status: 404 }
      );
    }

    // Calculate stats
    const totalMessages = await prisma.message.count({
      where: {
        conversation: {
          lead_id: lead.id
        }
      }
    });

    const lastMessage = await prisma.message.findFirst({
      where: {
        conversation: {
          lead_id: lead.id
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    // Format the response
    const formattedLead = {
      id: lead.id,
      phone_number_id: lead.phone_number_id,
      client_id: lead.client_id,
      name: lead.name,
      email: lead.email,
      notes: lead.notes,
      created_at: lead.created_at.toISOString(),
      updated_at: lead.updated_at.toISOString(),
      phone_number: {
        id: lead.phone_number.id,
        number: formatToE164(lead.phone_number.number),
        status: lead.phone_number.status,
        created_at: lead.phone_number.created_at.toISOString(),
        updated_at: lead.phone_number.updated_at.toISOString(),
      },
      client: {
        id: lead.client.id,
        name: lead.client.name || 'Unknown Client',
        business_type: lead.client.business_type ? {
          id: lead.client.business_type.id,
          name: lead.client.business_type.name,
        } : null,
      },
      conversations: lead.conversations.map(conv => ({
        id: conv.id,
        agent_id: conv.agent_id,
        status: conv.status,
        created_at: conv.created_at.toISOString(),
        updated_at: conv.updated_at.toISOString(),
        agent: {
          id: conv.agent.id,
          label: conv.agent.label,
          phone_number: formatToE164(conv.agent.phone_number.number),
        },
        lastMessage: conv.messages[0] ? {
          body: conv.messages[0].body,
          direction: conv.messages[0].direction,
          created_at: conv.messages[0].created_at.toISOString(),
        } : null,
      })),
      calls: lead.calls.map(call => ({
        id: call.id,
        call_sid: call.call_sid,
        direction: call.direction,
        status: call.status,
        from_number: call.from_number,
        to_number: call.to_number,
        duration: call.duration,
        started_at: call.started_at.toISOString(),
        answered_at: call.answered_at?.toISOString() || null,
      })),
      stats: {
        totalConversations: lead._count.conversations,
        totalMessages: totalMessages,
        totalCalls: lead._count.calls,
        lastContactDate: lastMessage?.created_at.toISOString() || lead.calls[0]?.started_at.toISOString(),
        lastMessageDirection: lastMessage?.direction as 'INBOUND' | 'OUTBOUND' | undefined,
      },
      _count: lead._count,
    };

    return NextResponse.json(formattedLead);

  } catch (error) {
    console.error('Error fetching lead:', error);
    return NextResponse.json(
      { error: 'Failed to fetch lead' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { name, email, notes } = body;

    // First get the current lead to access the person
    const currentLead = await prisma.lead.findUnique({
      where: { id },
      include: { person: true }
    });

    if (!currentLead) {
      return NextResponse.json(
        { error: 'Lead not found' },
        { status: 404 }
      );
    }

    // Update person data if name or email changed
    if ((name !== undefined && name !== currentLead.person?.name) ||
        (email !== undefined && email !== currentLead.person?.email)) {
      await updatePerson(currentLead.person_id, {
        name: name !== undefined ? name : currentLead.person?.name || undefined,
        email: email !== undefined ? email : currentLead.person?.email || undefined,
      });
    }

    // Update lead notes if provided
    const updatedLead = await prisma.lead.update({
      where: { id },
      data: {
        notes: notes !== undefined ? notes : undefined,
        updated_at: new Date(),
      },
      include: {
        phone_number: true,
        person: true,
        client: {
          include: {
            business_type: true
          }
        },
        _count: {
          select: {
            conversations: true,
            calls: true
          }
        }
      }
    });

    // Format the response
    const formattedLead = {
      id: updatedLead.id,
      phone_number_id: updatedLead.phone_number_id,
      client_id: updatedLead.client_id,
      name: updatedLead.name,
      email: updatedLead.email,
      notes: updatedLead.notes,
      created_at: updatedLead.created_at.toISOString(),
      updated_at: updatedLead.updated_at.toISOString(),
      phone_number: {
        id: updatedLead.phone_number.id,
        number: formatToE164(updatedLead.phone_number.number),
        status: updatedLead.phone_number.status,
        created_at: updatedLead.phone_number.created_at.toISOString(),
        updated_at: updatedLead.phone_number.updated_at.toISOString(),
      },
      client: {
        id: updatedLead.client.id,
        name: updatedLead.client.name || 'Unknown Client',
        business_type: updatedLead.client.business_type ? {
          id: updatedLead.client.business_type.id,
          name: updatedLead.client.business_type.name,
        } : null,
      },
      _count: updatedLead._count,
    };

    return NextResponse.json(formattedLead);

  } catch (error) {
    console.error('Error updating lead:', error);
    
    if (error instanceof Error && error.message.includes('Record to update not found')) {
      return NextResponse.json(
        { error: 'Lead not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update lead' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;

    // Check if lead exists and has conversations
    const lead = await prisma.lead.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            conversations: true,
            calls: true
          }
        }
      }
    });

    if (!lead) {
      return NextResponse.json(
        { error: 'Lead not found' },
        { status: 404 }
      );
    }

    // If lead has conversations or calls, we might want to prevent deletion
    // or handle it differently (soft delete, etc.)
    if (lead._count.conversations > 0 || lead._count.calls > 0) {
      return NextResponse.json(
        { error: 'Cannot delete lead with existing conversations or calls' },
        { status: 409 }
      );
    }

    await prisma.lead.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Lead deleted successfully' });

  } catch (error) {
    console.error('Error deleting lead:', error);
    return NextResponse.json(
      { error: 'Failed to delete lead' },
      { status: 500 }
    );
  }
}
