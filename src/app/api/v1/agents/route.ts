import {NextResponse} from "next/server";
import {PhoneNumber} from "@/components/phoneNumber/types/PhoneNumberType";
import {sql} from "@/app/lib/data";
import { PrismaClient } from '@/../generated/prisma';

// Create a single PrismaClient instance and reuse it
const prisma = new PrismaClient();

export async function GET() {
    const agents = await prisma.agent.findMany({
        include: {
            phone_number:{
                select: {
                    id: true,
                    number: true,
                    status: true,
                }
            },
            forward_number: {
                select: {
                    id: true,
                    number: true,
                    status: true,
                }
            },
            auto_responses: {
                select: {
                    enabled: true,
                    message_prompt: true,
                    response_delay: true
                }
            }
        }
    }).then((data) => {
        return data.map((agent) => ({
            id: agent.id.toString(), // Convert BigInt to string
            label: agent.label,
            status: agent.status,
            phoneNumber: {
                id: agent.phone_number.id.toString(), // Convert BigInt to string
                number: agent.phone_number.number.toString(),
                status: agent.phone_number.status
            },
            forwardNumber: agent.forward_number ? {
                id: agent.forward_number.id.toString(), // Convert BigInt to string
                number: agent.forward_number.number.toString(),
                status: agent.forward_number.status
            } : null,
            autoReply: agent.auto_responses.length > 0 ? {
                enabled: agent.auto_responses[0].enabled,
                message: agent.auto_responses[0].message_prompt,
                responseDelay: agent.auto_responses[0].response_delay
            } : null
        }))
    });

    return NextResponse.json(agents);
}
