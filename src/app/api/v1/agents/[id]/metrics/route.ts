import {NextResponse} from "next/server";
import {sql} from "@/app/lib/data";
import { PrismaClient } from '@/../generated/prisma';
import {AgentMetrics} from "@/app/agents/types/AgentType";

const prisma = new PrismaClient();

export async function GET(request: Request, {params}: {params: { id: string }})
{
    const {id} = await params;

    try {
        // First, get the phone_number_id from the agent
        const agent = await prisma.agent.findUnique({
            where: {
                id: id
            },
            include: {
                phone_number: true
            }
        });

        if (!agent) {
            return NextResponse.json(
                {error: "Agent not found."},
                {status: 404}
            );
        }

        // Get conversations count
        const conversationsCount = await prisma.conversation.count({
            where: {
                agent_id: id,
                deleted_at: null
            }
        });

        // Get messages statistics
        const [messagesSentResult, messagesReceivedResult] = await Promise.all([
            prisma.message.count({
                where: {
                    conversation: {
                        agent_id: id
                    },
                    direction: 'OUTBOUND'
                }
            }),
            prisma.message.count({
                where: {
                    conversation: {
                        agent_id: id
                    },
                    direction: 'INBOUND'
                }
            })
        ]);

        // Get failed messages count (assuming status 'failed' or 'error')
        const messagesFailedCount = await prisma.message.count({
            where: {
                conversation: {
                    agent_id: id
                },
                status: {
                    in: ['failed', 'error', 'undelivered']
                }
            }
        });

        // Calculate total message cost for this agent
        const totalMessageCost = await prisma.message.aggregate({
            where: {
                conversation: {
                    agent_id: id
                }
            },
            _sum: {
                cost: true
            }
        });

        // Calculate generation cost for this agent's client
        const totalGenerationCost = await prisma.generation.aggregate({
            where: {
                client: {
                    agents: {
                        some: {
                            id: id
                        }
                    }
                }
            },
            _sum: {
                cost: true
            }
        });

        // Calculate call costs
        const totalCallCost = await prisma.call.aggregate({
            where: {
                agent_id: id
            },
            _sum: {
                price: true
            }
        });

        // Get current month's costs
        const currentMonth = new Date();
        currentMonth.setDate(1);
        currentMonth.setHours(0, 0, 0, 0);

        const monthToDateMessageCost = await prisma.message.aggregate({
            where: {
                conversation: {
                    agent_id: id
                },
                created_at: {
                    gte: currentMonth
                }
            },
            _sum: {
                cost: true
            }
        });

        const monthToDateCallCost = await prisma.call.aggregate({
            where: {
                agent_id: id,
                created_at: {
                    gte: currentMonth
                }
            },
            _sum: {
                price: true
            }
        });

        // Calculate average response time (mock calculation based on message timestamps)
        const recentConversations = await prisma.conversation.findMany({
            where: {
                agent_id: id,
                deleted_at: null
            },
            include: {
                messages: {
                    orderBy: {
                        created_at: 'asc'
                    },
                    take: 10
                }
            },
            take: 20,
            orderBy: {
                created_at: 'desc'
            }
        });

        let totalResponseTime = 0;
        let responseCount = 0;

        recentConversations.forEach(conversation => {
            const messages = conversation.messages;
            for (let i = 1; i < messages.length; i++) {
                if (messages[i-1].direction === 'inbound' && messages[i].direction === 'outbound') {
                    const responseTime = (new Date(messages[i].created_at).getTime() -
                                        new Date(messages[i-1].created_at).getTime()) / 1000;
                    totalResponseTime += responseTime;
                    responseCount++;
                }
            }
        });

        const averageResponseTime = responseCount > 0 ? Math.round(totalResponseTime / responseCount) : 0;

        // Calculate reputation score based on actual metrics
        const totalMessages = messagesSentResult + messagesReceivedResult;
        const failureRate = totalMessages > 0 ? messagesFailedCount / totalMessages : 0;
        const responseTimeScore = Math.max(0, 10 - (averageResponseTime / 60)); // Lower is better
        const activityScore = Math.min(10, conversationsCount / 10); // More conversations = better

        const reputationScore = Math.max(0, Math.min(10,
            (responseTimeScore * 0.4) + (activityScore * 0.4) + ((1 - failureRate) * 10 * 0.2)
        ));

        let reputationStatus = "Fair";
        if (reputationScore >= 8) reputationStatus = "Excellent";
        else if (reputationScore >= 6) reputationStatus = "Good";

        // Calculate total costs
        const messageCost = Number(totalMessageCost._sum.cost || 0);
        const generationCost = Number(totalGenerationCost._sum.cost || 0);
        const callCost = Number(totalCallCost._sum.price || 0);
        const monthlyCost = Number(agent.phone_number.monthly_cost || 0);

        const totalCost = messageCost + generationCost + callCost + monthlyCost;

        const monthToDateTotal = Number(monthToDateMessageCost._sum.cost || 0) +
                               Number(monthToDateCallCost._sum.price || 0) +
                               monthlyCost;

        const metrics: AgentMetrics = {
            id: id,
            conversations: conversationsCount,
            messagesSent: messagesSentResult,
            messagesReceived: messagesReceivedResult,
            messagesFailed: messagesFailedCount,
            averageClientResponseTimeSeconds: averageResponseTime,
            cost: {
                total: parseFloat(totalCost.toFixed(2)),
                monthToDate: parseFloat(monthToDateTotal.toFixed(2))
            },
            reputation: {
                score: parseFloat(reputationScore.toFixed(1)),
                status: reputationStatus,
                lastUpdated: new Date().toISOString()
            }
        };

        return NextResponse.json(metrics);
    } catch (error) {
        console.error("Error fetching agent metrics:", error);
        return NextResponse.json(
            {error: "Failed to fetch agent metrics."},
            {status: 500}
        );
    }
}
