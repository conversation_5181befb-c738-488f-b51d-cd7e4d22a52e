import {NextResponse} from "next/server";
import {sql} from "@/app/lib/data";
import { PrismaClient } from '@/../generated/prisma';
import { Agent } from "@/app/agents/types/AgentType";

// Create a single PrismaClient instance and reuse it
const prisma = new PrismaClient();

export async function GET(request: Request, {params}: { params: { id: string } }) {
    const {id} = await params;

    try {
        // Fetch agent with related data using Prisma
        const agent = await prisma.agent.findUnique({
            where: {
                id: id
            },
            include: {
                phone_number: true,
                forward_number: true,
                auto_responses: {
                    orderBy: {
                        created_at: 'desc'
                    },
                    take: 1
                },
                client: true
            }
        });

        if (!agent) {
            return NextResponse.json(
                {error: "Agent not found."},
                {status: 404}
            );
        }

        // Transform the data to match the Agent type
        const transformedAgent: Agent = {
            id: agent.id,
            label: agent.label,
            phoneNumber: {
                id: agent.phone_number.id,
                number: agent.phone_number.number.toString(),
                status: agent.phone_number.status.toLowerCase() as "active" | "inactive"
            },
            forwardNumber: agent.forward_number ? {
                id: agent.forward_number.id,
                number: agent.forward_number.number.toString(),
                status: agent.forward_number.status.toLowerCase() as "active" | "inactive"
            } : null,
            status: agent.status.toLowerCase() as "active" | "inactive",
            // purchaseDate: agent.phone_number.purchase_date ? agent.phone_number.purchase_date.toISOString() : null,
            // monthlyCost: agent.phone_number.monthly_cost ? parseFloat(agent.phone_number.monthly_cost.toString()) : null,
            autoReply: {
                enabled: agent.auto_responses.length > 0 ? !!agent.auto_responses[0].enabled : false,
                message: agent.auto_responses.length > 0 ? agent.auto_responses[0].message_prompt : "",
                responseDelay: agent.auto_responses.length > 0 ? agent.auto_responses[0].response_delay : "immediate"
            },
            client: agent.client ? {
                id: agent.client.id,
                name: agent.client.name || ""
            } : undefined
        };

        return NextResponse.json(transformedAgent);
    } catch (error) {
        console.error("Error fetching agent:", error);
        return NextResponse.json(
            {error: "Failed to fetch agent details."},
            {status: 500}
        );
    }
}

export async function PUT(request: Request, {params}: { params: { id: string } }) {
    const {id} = await params;

    try {
        const body = await request.json();
        const { label, forwardNumberId, monthlyCost, purchaseDate, autoReply } = body;

        // Start a transaction to update both agent and auto-reply
        const result = await prisma.$transaction(async (tx) => {
            // Update agent details
            const updatedAgent = await tx.agent.update({
                where: {
                    id: id
                },
                data: {
                    label: label,
                    forward_number_id: forwardNumberId,
                    // monthly_cost: monthlyCost ? parseFloat(monthlyCost) : null,
                    // purchase_date: purchaseDate ? new Date(purchaseDate) : null,
                },
                include: {
                    phone_number: true,
                    forward_number: true,
                    client: true
                }
            });

            // Handle auto-reply settings if provided
            if (autoReply) {
                // Check if there's an existing auto-response
                const existingAutoResponse = await tx.auto_response.findFirst({
                    where: {
                        agent_id: id
                    },
                    orderBy: {
                        created_at: 'desc'
                    }
                });

                if (existingAutoResponse) {
                    // Update existing auto-response
                    await tx.auto_response.update({
                        where: {
                            id: existingAutoResponse.id
                        },
                        data: {
                            enabled: autoReply.enabled,
                            message_prompt: autoReply.message,
                            response_delay: autoReply.responseDelay.toUpperCase()
                        }
                    });
                } else if (autoReply.enabled) {
                    // Create new auto-response if none exists and it's enabled
                    await tx.auto_response.create({
                        data: {
                            agent_id: id,
                            enabled: autoReply.enabled,
                            message_prompt: autoReply.message,
                            response_delay: autoReply.responseDelay.toUpperCase()
                        }
                    });
                }
            }

            // Get the updated auto-responses
            const autoResponses = await tx.auto_response.findMany({
                where: {
                    agent_id: id
                },
                orderBy: {
                    created_at: 'desc'
                },
                take: 1
            });

            return {
                agent: updatedAgent,
                autoResponses
            };
        });

        // Transform the data to match the Agent type
        const transformedAgent: Agent = {
            id: result.agent.id,
            label: result.agent.label,
            phoneNumber: {
                id: result.agent.phone_number.id,
                number: result.agent.phone_number.number.toString(),
                status: result.agent.phone_number.status.toLowerCase() as "active" | "inactive"
            },
            forwardNumber: result.agent.forward_number ? {
                id: result.agent.forward_number.id,
                number: result.agent.forward_number.number.toString(),
                status: result.agent.forward_number.status.toLowerCase() as "active" | "inactive"
            } : null,
            status: result.agent.phone_number.status.toLowerCase() as "active" | "inactive",
            // purchaseDate: result.agent.purchase_date ? result.agent.purchase_date.toISOString() : null,
            // monthlyCost: result.agent.monthly_cost ? parseFloat(result.agent.monthly_cost.toString()) : null,
            autoReply: {
                enabled: result.autoResponses.length > 0 ? !!result.autoResponses[0].enabled : false,
                message: result.autoResponses.length > 0 ? result.autoResponses[0].message_prompt : "",
                responseDelay: result.autoResponses.length > 0 ? result.autoResponses[0].response_delay.toLowerCase() : "immediate"
            },
            client: result.agent.client ? {
                id: result.agent.client.id,
                name: result.agent.client.name || ""
            } : undefined
        };

        return NextResponse.json(transformedAgent);
    } catch (error) {
        console.error("Error updating agent:", error);
        return NextResponse.json(
            {error: "Failed to update agent."},
            {status: 500}
        );
    }
}

export async function DELETE(request: Request, {params}: { params: { id: string } }) {
    const {id} = await params;

    try {
        // Start a transaction to delete related records first
        await prisma.$transaction(async (tx) => {
            // Delete auto-responses associated with this agent
            await tx.auto_response.deleteMany({
                where: {
                    agent_id: id
                }
            });

            // Delete the agent
            await tx.agent.delete({
                where: {
                    id: id
                }
            });
        });

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Error deleting agent:", error);
        return NextResponse.json(
            {error: "Failed to delete agent."},
            {status: 500}
        );
    }
}
