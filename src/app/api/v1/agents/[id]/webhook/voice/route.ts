import { NextResponse } from "next/server";
import { PrismaClient } from '@/../generated/prisma';
import twilio from 'twilio';

const VoiceResponse = twilio.twiml.VoiceResponse;
const prisma = new PrismaClient();

export async function POST(request: Request, { params }: { params: { id: string } }) {
    const { id } = await params;

    try {
        // Parse the incoming form data from Twilio
        const formData = await request.formData();
        const callSid = formData.get('CallSid') as string;
        const from = formData.get('From') as string;
        const to = formData.get('To') as string;
        const callStatus = formData.get('CallStatus') as string;

        console.log(`Incoming call for agent ${id}:`, {
            callSid,
            from,
            to,
            callStatus
        });

        // Get agent details to find the client
        const agent = await prisma.agent.findUnique({
            where: { id },
            include: { client: true }
        });

        if (!agent) {
            throw new Error('Agent not found');
        }

        // Create or find lead for this caller
        const lead = await createOrFindLead(from, agent.client_id);

        // Create call record
        await prisma.call.create({
            data: {
                agent_id: id,
                lead_id: lead.id,
                client_id: agent.client_id,
                call_sid: callSid,
                direction: 'INBOUND',
                status: 'IN_PROGRESS',
                from_number: from,
                to_number: to,
                started_at: new Date()
            }
        });

        console.log('Call record created:', callSid);

        // Create a new TwiML response
        const twiml = new VoiceResponse();

        // Add a greeting and voicemail prompt
        twiml.say({
            voice: 'alice',
            language: 'en-US'
        }, 'Hello! Thank you for calling. I\'m not available to take your call right now, but your call is important to me. Please leave a detailed message after the beep, and I\'ll get back to you as soon as possible.');

        // Record the voicemail
        twiml.record({
            action: `/api/v1/agents/${id}/webhook/recording`,
            method: 'POST',
            maxLength: 120, // 2 minutes max
            timeout: 10, // Wait 10 seconds for caller to start speaking
            transcribe: true,
            transcribeCallback: `/api/v1/agents/${id}/webhook/transcription`,
            playBeep: true,
            finishOnKey: '#'
        });

        // Fallback message if no recording is made
        twiml.say({
            voice: 'alice',
            language: 'en-US'
        }, 'I didn\'t receive your message. Please try calling again. Goodbye!');

        // Hang up
        twiml.hangup();

        // Return the TwiML response
        return new NextResponse(twiml.toString(), {
            headers: {
                'Content-Type': 'text/xml',
            },
        });

    } catch (error) {
        console.error('Error handling voice webhook:', error);

        // Return a simple fallback TwiML in case of error
        const twiml = new VoiceResponse();
        twiml.say({
            voice: 'alice',
            language: 'en-US'
        }, 'Sorry, we\'re experiencing technical difficulties. Please try calling again later. Goodbye!');
        twiml.hangup();

        return new NextResponse(twiml.toString(), {
            headers: {
                'Content-Type': 'text/xml',
            },
        });
    }
}

// Helper function to create or find lead
async function createOrFindLead(phoneNumberStr: string, clientId: string) {
    try {
        // Clean the phone number (remove +1 prefix if present)
        const cleanPhoneNumber = phoneNumberStr.replace(/^\+1/, '');
        const phoneNumberBigInt = BigInt(cleanPhoneNumber);

        // Check if phone number already exists
        let phoneNumber = await prisma.phone_number.findFirst({
            where: { number: phoneNumberBigInt }
        });

        // Create phone number if it doesn't exist
        if (!phoneNumber) {
            phoneNumber = await prisma.phone_number.create({
                data: {
                    number: phoneNumberBigInt,
                    status: 'ACTIVE'
                }
            });
            console.log('Created new phone number:', phoneNumber.id);
        }

        // Check if lead already exists for this phone number and client
        let lead = await prisma.lead.findFirst({
            where: {
                phone_number_id: phoneNumber.id,
                client_id: clientId
            }
        });

        // Create lead if it doesn't exist
        if (!lead) {
            lead = await prisma.lead.create({
                data: {
                    phone_number_id: phoneNumber.id,
                    client_id: clientId,
                    name: null,
                    email: null,
                    notes: `Lead created from incoming call on ${new Date().toISOString()}`
                }
            });
            console.log('Created new lead:', lead.id);
        } else {
            // Update existing lead with new call activity
            await prisma.lead.update({
                where: { id: lead.id },
                data: {
                    notes: `${lead.notes || ''}\nIncoming call on ${new Date().toISOString()}`,
                    updated_at: new Date()
                }
            });
            console.log('Updated existing lead:', lead.id);
        }

        return lead;
    } catch (error) {
        console.error('Error creating/finding lead:', error);
        throw error;
    }
}
