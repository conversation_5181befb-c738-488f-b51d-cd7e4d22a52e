import { NextResponse } from "next/server";
import { PrismaClient } from '@/../generated/prisma';
import twilio from 'twilio';
import { uploadRecording } from "@/app/api/v1/_components/s3";
import { findOrCreateLead } from "@/app/api/v1/_components/lead";

const VoiceResponse = twilio.twiml.VoiceResponse;
const prisma = new PrismaClient();

export async function POST(request: Request, { params }: { params: { id: string } }) {
    const { id } = await params;

    try {
        const formData = await request.formData();
        const recordingUrl = formData.get('RecordingUrl') as string;
        const recordingSid = formData.get('RecordingSid') as string;
        const callSid = formData.get('CallSid') as string;
        const recordingDuration = formData.get('RecordingDuration') as string;

        console.log('Recording received:', {
            agentId: id,
            recordingSid,
            callSid,
            recordingUrl,
            duration: recordingDuration
        });

        // Find the call record with related data
        const call = await prisma.call.findFirst({
            where: {
                call_sid: callSid,
                agent_id: id
            },
            include: {
                agent: {
                    include: {
                        client: true
                    }
                },
                lead: true
            }
        });

        if (!call) {
            throw new Error('Call record not found');
        }

        // Create voicemail record linked to the call
        const voicemail = await prisma.voicemail.create({
            data: {
                call_id: call.id,
                recording_sid: recordingSid,
                recording_url: recordingUrl,
                duration: parseInt(recordingDuration) || 0,
                created_at: new Date()
            }
        });

        // Upload recording to S3 with metadata
        try {
            const s3Result = await uploadRecording({
                recordingUrl,
                clientId: call.agent.client_id,
                agentId: call.agent_id,
                callId: call.id,
                voicemailId: voicemail.id,
                leadId: call.lead_id,
            });

            console.log('Recording uploaded to S3:', s3Result);
        } catch (s3Error) {
            console.error('Failed to upload recording to S3:', s3Error);
            // Continue processing even if S3 upload fails
        }

        // Update call status to completed
        await prisma.call.update({
            where: { id: call.id },
            data: {
                status: 'COMPLETED',
                duration: parseInt(recordingDuration) || 0,
                ended_at: new Date()
            }
        });

        console.log('Voicemail saved:', voicemail.id);
        console.log('Call updated:', call.id);

        // Create TwiML response to thank the caller
        const twiml = new VoiceResponse();
        twiml.say({
            voice: 'alice',
            language: 'en-US'
        }, 'Thank you for your message. I\'ll get back to you soon. Goodbye!');
        twiml.hangup();

        return new NextResponse(twiml.toString(), {
            headers: {
                'Content-Type': 'text/xml',
            },
        });

    } catch (error) {
        console.error('Error handling recording webhook:', error);

        const twiml = new VoiceResponse();
        twiml.say({
            voice: 'alice',
            language: 'en-US'
        }, 'Thank you for calling. Goodbye!');
        twiml.hangup();

        return new NextResponse(twiml.toString(), {
            headers: {
                'Content-Type': 'text/xml',
            },
        });
    }
}
