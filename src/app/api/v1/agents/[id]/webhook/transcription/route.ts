import { NextResponse } from "next/server";
import { PrismaClient } from '@/../generated/prisma';
import twilio from 'twilio';
import { formatToE164 } from "@/components/phoneNumber/phoneFormatters";
import { generateAgentResponse, getResponseDelayMs } from "@/app/api/v1/_components/agent/openai-agent";
import { updateRecordingMetadata } from "@/app/api/v1/_components/s3";
import { findOrCreateLead, findOrCreateConversation, updateLeadNotes } from "@/app/api/v1/_components/lead";

const prisma = new PrismaClient();

// Initialize Twilio client
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

export async function POST(request: Request, { params }: { params: { id: string } }) {
    const { id } = await params;

    try {
        const formData = await request.formData();
        const transcriptionText = formData.get('TranscriptionText') as string;
        const transcriptionSid = formData.get('TranscriptionSid') as string;
        const recordingSid = formData.get('RecordingSid') as string;
        const callSid = formData.get('CallSid') as string;

        console.log('Transcription received:', {
            agentId: id,
            transcriptionSid,
            recordingSid,
            callSid,
            transcriptionText
        });

        // Update the voicemail record with transcription
        const updatedVoicemail = await prisma.voicemail.updateMany({
            where: {
                recording_sid: recordingSid,
            },
            data: {
                transcription: transcriptionText,
                transcription_sid: transcriptionSid
            }
        });

        // Find the voicemail to get the caller number and update lead notes
        const voicemail = await prisma.voicemail.findFirst({
            where: {
                recording_sid: recordingSid,
            },
            include: {
                call: {
                    include: {
                        agent: {
                            include: {
                                client: {
                                    include: {
                                        business_type: true
                                    }
                                },
                                phone_number: true,
                                auto_responses: {
                                    orderBy: {
                                        created_at: 'desc',
                                    },
                                    take: 1,
                                }
                            }
                        },
                        client: true
                    }
                }
            }
        });

        if (voicemail && transcriptionText) {
            // Find or create lead using the reusable component
            const leadResult = await findOrCreateLead({
                phoneNumber: voicemail.call.from_number,
                clientId: voicemail.call.agent.client_id,
                agentId: voicemail.call.agent.id,
            });

            const lead = leadResult.lead;

            // Add transcription to lead notes
            const transcriptionNote = `Voicemail Transcription (${new Date().toISOString()}):\n"${transcriptionText}"`;
            await updateLeadNotes(lead.id, transcriptionNote);
            console.log('Updated lead with transcription:', lead.id);

            // Send SMS response based on the transcription
            const agent = voicemail.call.agent;

            if (agent && agent.auto_responses.length > 0 && agent.auto_responses[0].enabled) {
                // Find or create a conversation using the reusable component
                const conversationResult = await findOrCreateConversation({
                    agentId: agent.id,
                    leadId: lead.id,
                    clientId: agent.client_id,
                });

                const conversation = conversationResult.conversation;

                // Generate AI response using the reusable component
                const aiResult = await generateAgentResponse({
                    agentId: id,
                    scenario: 'voicemail',
                    transcriptionText,
                    userMessage: "I just left you a voicemail.",
                    conversationHistory: {
                        id: conversation.id,
                        messages: conversation.messages
                    }
                });

                const aiResponse = aiResult.response;

                // Apply response delay if configured
                if (agent.auto_responses[0].response_delay !== 'IMMEDIATE') {
                    const delayMs = getResponseDelayMs(agent.auto_responses[0].response_delay);
                    await new Promise(resolve => setTimeout(resolve, delayMs));
                }

                // Send the response via Twilio
                const twilioResponse = await twilioClient.messages.create({
                    body: aiResponse,
                    from: voicemail.call.to_number,
                    to: voicemail.call.from_number,
                });

                // Store the outgoing message
                await prisma.message.create({
                    data: {
                        conversation_id: conversation.id,
                        client_id: agent.client_id,
                        phone_number_id: agent.phone_number_id,
                        body: aiResponse,
                        status: 'delivered',
                        direction: 'OUTBOUND',
                        is_read: true,
                        cost: 0.0
                    }
                });

                console.log(`Sent SMS response to voicemail from ${voicemail.call.from_number}: ${aiResponse}`);

                // Update S3 recording metadata with transcription and conversation IDs
                try {
                    await updateRecordingMetadata({
                        clientId: agent.client_id,
                        agentId: agent.id,
                        voicemailId: voicemail.id,
                        transcriptionId: transcriptionSid,
                        conversationId: conversation.id,
                    });
                    console.log('Updated S3 recording metadata');
                } catch (s3Error) {
                    console.error('Failed to update S3 recording metadata:', s3Error);
                    // Continue processing even if S3 update fails
                }
            }
        }

        console.log('Voicemail transcription updated:', updatedVoicemail);

        return NextResponse.json({ success: true });

    } catch (error) {
        console.error('Error handling transcription webhook:', error);
        return NextResponse.json({ error: 'Failed to process transcription' }, { status: 500 });
    }
}


