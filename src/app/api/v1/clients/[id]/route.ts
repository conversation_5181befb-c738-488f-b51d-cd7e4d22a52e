import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";

export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = await params;

        const client = await prisma.client.findUnique({
            where: { id },
            include: {
                business_type: true
            }
        });

        if (!client) {
            return NextResponse.json(
                { error: "Client not found" },
                { status: 404 }
            );
        }

        return NextResponse.json({
            id: client.id,
            name: client.name || '',
            businessType: client.business_type ? {
                id: client.business_type.id,
                name: client.business_type.name,
                description: client.business_type.description || undefined
            } : undefined,
            companySize: client.company_size || undefined,
            isMobile: client.is_mobile || false,
            offersEmergency: client.offers_emergency || false,
            createdAt: client.created_at?.toISOString() || new Date().toISOString(),
            updatedAt: client.updated_at?.toISOString()
        });
    } catch (error) {
        console.error("Error fetching client:", error);
        return NextResponse.json(
            { error: "Failed to fetch client" },
            { status: 500 }
        );
    }
}

export async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = await params;
        const body = await request.json();

        const { name, businessTypeId, companySize, isMobile, offersEmergency } = body;

        const client = await prisma.client.update({
            where: { id },
            data: {
                name: name !== undefined ? name : undefined,
                business_type_id: businessTypeId !== undefined ? businessTypeId : undefined,
                company_size: companySize !== undefined ? companySize : undefined,
                is_mobile: isMobile !== undefined ? isMobile : undefined,
                offers_emergency: offersEmergency !== undefined ? offersEmergency : undefined,
                updated_at: new Date()
            },
            include: {
                business_type: true
            }
        });

        return NextResponse.json({
            id: client.id,
            name: client.name || '',
            businessType: client.business_type ? {
                id: client.business_type.id,
                name: client.business_type.name,
                description: client.business_type.description || undefined
            } : undefined,
            companySize: client.company_size || undefined,
            isMobile: client.is_mobile || false,
            offersEmergency: client.offers_emergency || false,
            createdAt: client.created_at?.toISOString() || new Date().toISOString(),
            updatedAt: client.updated_at?.toISOString()
        });
    } catch (error) {
        console.error("Error updating client:", error);
        return NextResponse.json(
            { error: "Failed to update client" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = await params;

        // Check if client exists
        const existingClient = await prisma.client.findUnique({
            where: { id }
        });

        if (!existingClient) {
            return NextResponse.json(
                { error: "Client not found" },
                { status: 404 }
            );
        }

        // Delete the client
        await prisma.client.delete({
            where: { id }
        });

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Error deleting client:", error);
        return NextResponse.json(
            { error: "Failed to delete client" },
            { status: 500 }
        );
    }
}
