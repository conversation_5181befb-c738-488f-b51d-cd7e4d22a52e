import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";

export async function GET() {
    try {
        const clients = await prisma.client.findMany({
            include: {
                business_type: true
            },
            orderBy: {
                name: 'asc'
            }
        });

        // Transform the data to match the Client type
        const transformedClients = clients.map(client => ({
            id: client.id,
            name: client.name || '',
            businessType: client.business_type ? {
                id: client.business_type.id,
                name: client.business_type.name,
                description: client.business_type.description || undefined
            } : undefined,
            companySize: client.company_size || undefined,
            isMobile: client.is_mobile || false,
            offersEmergency: client.offers_emergency || false,
            createdAt: client.created_at?.toISOString() || new Date().toISOString(),
            updatedAt: client.updated_at?.toISOString()
        }));

        return NextResponse.json(transformedClients);
    } catch (error) {
        console.error("Error fetching clients:", error);
        return NextResponse.json(
            { error: "Failed to fetch clients" },
            { status: 500 }
        );
    }
}

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        
        const { name, businessTypeId, companySize, isMobile, offersEmergency } = body;
        
        if (!name) {
            return NextResponse.json(
                { error: "Client name is required" },
                { status: 400 }
            );
        }
        
        const client = await prisma.client.create({
            data: {
                name,
                business_type_id: businessTypeId || null,
                company_size: companySize || null,
                is_mobile: isMobile || false,
                offers_emergency: offersEmergency || false
            },
            include: {
                business_type: true
            }
        });
        
        return NextResponse.json({
            id: client.id,
            name: client.name || '',
            businessType: client.business_type ? {
                id: client.business_type.id,
                name: client.business_type.name,
                description: client.business_type.description || undefined
            } : undefined,
            companySize: client.company_size || undefined,
            isMobile: client.is_mobile || false,
            offersEmergency: client.offers_emergency || false,
            createdAt: client.created_at?.toISOString() || new Date().toISOString(),
            updatedAt: client.updated_at?.toISOString()
        });
    } catch (error) {
        console.error("Error creating client:", error);
        return NextResponse.json(
            { error: "Failed to create client" },
            { status: 500 }
        );
    }
}