import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";

export async function GET() {
    try {
        const businessTypes = await prisma.business_type.findMany({
            orderBy: {
                name: 'asc'
            }
        });

        return NextResponse.json(businessTypes);
    } catch (error) {
        console.error("Error fetching business types:", error);
        return NextResponse.json(
            { error: "Failed to fetch business types" },
            { status: 500 }
        );
    }
}