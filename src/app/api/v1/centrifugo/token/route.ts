import { NextResponse } from "next/server";
import { generateCentrifugoToken } from "../../_components/centrifugo";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { userId, conversationIds = [] } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Generate JWT token for Centrifugo authentication
    const token = generateCentrifugoToken(userId, conversationIds);

    return NextResponse.json({
      token,
      expires_in: 24 * 60 * 60, // 24 hours in seconds
    });

  } catch (error) {
    console.error('Error generating Centrifugo token:', error);
    return NextResponse.json(
      { error: 'Failed to generate token' },
      { status: 500 }
    );
  }
}
