import { PrismaClient } from '@/../generated/prisma';

const prisma = new PrismaClient();

export interface CreatePersonData {
  name?: string;
  email?: string;
  clientId: string;
}

export interface UpdatePersonData {
  name?: string;
  email?: string;
}

/**
 * Creates a new person record
 */
export async function createPerson(data: CreatePersonData) {
  // Validate email format if provided
  if (data.email && !isValidEmail(data.email)) {
    throw new Error('Invalid email format');
  }

  return await prisma.person.create({
    data: {
      name: data.name,
      email: data.email,
      client_id: data.clientId,
    },
  });
}

/**
 * Updates a person record
 */
export async function updatePerson(personId: string, data: UpdatePersonData) {
  // Validate email format if provided
  if (data.email && !isValidEmail(data.email)) {
    throw new Error('Invalid email format');
  }

  return await prisma.person.update({
    where: { id: personId },
    data: {
      name: data.name,
      email: data.email,
      updated_at: new Date(),
    },
  });
}

/**
 * Gets a person by ID
 */
export async function getPersonById(personId: string) {
  return await prisma.person.findUnique({
    where: { id: personId },
    include: {
      leads: {
        include: {
          phone_number: true,
        },
      },
    },
  });
}

/**
 * Gets a person by email
 */
export async function getPersonByEmail(email: string, clientId: string) {
  return await prisma.person.findFirst({
    where: {
      email,
      client_id: clientId,
    },
  });
}

/**
 * Finds or creates a person based on name/email
 */
export async function findOrCreatePerson(data: CreatePersonData) {
  // If email is provided, try to find existing person by email
  if (data.email) {
    const existingPerson = await getPersonByEmail(data.email, data.clientId);
    if (existingPerson) {
      // Update name if provided and different
      if (data.name && existingPerson.name !== data.name) {
        return await updatePerson(existingPerson.id, { name: data.name });
      }
      return existingPerson;
    }
  }

  // Create new person
  return await createPerson(data);
}

/**
 * Syncs person data with all associated leads
 */
export async function syncPersonWithLeads(personId: string) {
  const person = await getPersonById(personId);
  if (!person) {
    throw new Error('Person not found');
  }

  // Note: Since leads don't have name/email fields directly anymore,
  // this function is mainly for future extensibility
  // The person data is now the source of truth for name/email
  
  return person;
}

/**
 * Validates email format
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Gets all people for a client
 */
export async function getPeopleByClient(clientId: string) {
  return await prisma.person.findMany({
    where: { client_id: clientId },
    include: {
      leads: {
        include: {
          phone_number: true,
          conversations: {
            include: {
              messages: {
                orderBy: { created_at: 'desc' },
                take: 1,
              },
            },
          },
        },
      },
    },
    orderBy: { created_at: 'desc' },
  });
}

/**
 * Deletes a person and handles cascade effects
 */
export async function deletePerson(personId: string) {
  // Note: This should be used carefully as it may affect leads
  // Consider soft delete or reassignment of leads
  return await prisma.person.delete({
    where: { id: personId },
  });
}
