import { PrismaClient } from '@/../generated/prisma';

const prisma = new PrismaClient();

export interface CreateMessageData {
  conversationId: string;
  body: string;
  direction: 'INBOUND' | 'OUTBOUND';
  phoneNumberId: string;
  clientId: string;
  createdByAgentId?: string;
  createdByPersonId?: string;
  status?: string;
}

export interface UpdateConversationData {
  status?: string;
  isAgentActive?: boolean;
}

/**
 * Creates a new message with proper creator tracking
 */
export async function createMessage(data: CreateMessageData) {
  // Validate that only one creator is specified
  if (data.createdByAgentId && data.createdByPersonId) {
    throw new Error('Message cannot be created by both agent and person');
  }

  if (!data.createdByAgentId && !data.createdByPersonId) {
    throw new Error('Message must have either agent or person creator');
  }

  return await prisma.message.create({
    data: {
      conversation_id: data.conversationId,
      body: data.body,
      direction: data.direction,
      phone_number_id: data.phoneNumberId,
      client_id: data.clientId,
      created_by_agent: data.createdByAgentId || null,
      created_by_person: data.createdByPersonId || null,
      status: data.status || 'delivered',
      is_read: data.direction === 'OUTBOUND', // Mark outbound messages as read by default
    },
    include: {
      agent: true,
      person: true,
    },
  });
}

/**
 * Creates a message from an automated agent
 */
export async function createAgentMessage(data: {
  conversationId: string;
  body: string;
  phoneNumberId: string;
  clientId: string;
  agentId: string;
}) {
  return await createMessage({
    ...data,
    direction: 'OUTBOUND',
    createdByAgentId: data.agentId,
    status: 'delivered',
  });
}

/**
 * Creates a message from a person (manual user input)
 */
export async function createPersonMessage(data: {
  conversationId: string;
  body: string;
  phoneNumberId: string;
  clientId: string;
  personId: string;
  direction: 'INBOUND' | 'OUTBOUND';
}) {
  // When a person creates a message, disable the agent for this conversation
  await updateConversation(data.conversationId, { isAgentActive: false });

  return await createMessage({
    ...data,
    createdByPersonId: data.personId,
    status: 'delivered',
  });
}

/**
 * Updates conversation properties
 */
export async function updateConversation(conversationId: string, data: UpdateConversationData) {
  return await prisma.conversation.update({
    where: { id: conversationId },
    data: {
      status: data.status,
      is_agent_active: data.isAgentActive,
      updated_at: new Date(),
    },
  });
}

/**
 * Toggles agent active status for a conversation
 */
export async function toggleConversationAgent(conversationId: string, isActive: boolean) {
  return await updateConversation(conversationId, { isAgentActive: isActive });
}

/**
 * Gets conversation with agent status
 */
export async function getConversationWithAgentStatus(conversationId: string) {
  return await prisma.conversation.findUnique({
    where: { id: conversationId },
    include: {
      agent: {
        include: {
          phone_number: true,
        },
      },
      lead: {
        include: {
          phone_number: true,
          person: true,
        },
      },
      messages: {
        include: {
          agent: true,
          person: true,
        },
        orderBy: { created_at: 'asc' },
      },
    },
  });
}

/**
 * Checks if automated responses should be sent for a conversation
 */
export async function shouldSendAutomatedResponse(conversationId: string): Promise<boolean> {
  const conversation = await prisma.conversation.findUnique({
    where: { id: conversationId },
    select: { is_agent_active: true },
  });

  return conversation?.is_agent_active ?? true;
}

/**
 * Gets recent messages for a conversation with creator information
 */
export async function getConversationMessages(conversationId: string, limit: number = 50) {
  return await prisma.message.findMany({
    where: { conversation_id: conversationId },
    include: {
      agent: {
        select: {
          id: true,
          label: true,
        },
      },
      person: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    orderBy: { created_at: 'asc' },
    take: limit,
  });
}

/**
 * Marks messages as read
 */
export async function markMessagesAsRead(messageIds: string[]) {
  return await prisma.message.updateMany({
    where: {
      id: { in: messageIds },
    },
    data: {
      is_read: true,
    },
  });
}

/**
 * Gets conversation statistics including agent activity
 */
export async function getConversationStats(conversationId: string) {
  const conversation = await prisma.conversation.findUnique({
    where: { id: conversationId },
    include: {
      messages: {
        include: {
          agent: true,
          person: true,
        },
      },
    },
  });

  if (!conversation) {
    return null;
  }

  const totalMessages = conversation.messages.length;
  const agentMessages = conversation.messages.filter(m => m.created_by_agent).length;
  const personMessages = conversation.messages.filter(m => m.created_by_person).length;
  const inboundMessages = conversation.messages.filter(m => m.direction === 'INBOUND').length;
  const outboundMessages = conversation.messages.filter(m => m.direction === 'OUTBOUND').length;

  return {
    conversationId,
    isAgentActive: conversation.is_agent_active,
    totalMessages,
    agentMessages,
    personMessages,
    inboundMessages,
    outboundMessages,
    lastMessageAt: conversation.messages[conversation.messages.length - 1]?.created_at,
  };
}
