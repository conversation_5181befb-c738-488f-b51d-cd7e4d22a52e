# S3 Service Verification Checklist

## ✅ Prerequisites Completed

- [x] AWS SDK v3 installed (`@aws-sdk/client-s3` and `@aws-sdk/s3-request-presigner`)
- [x] TypeScript compilation successful
- [x] All AWS SDK imports uncommented and active
- [x] Type definitions fixed for AWS S3 compatibility

## 🔧 Environment Setup Required

### 1. Environment Variables
Set these in your `.env` file or environment:

```env
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
```

### 2. AWS S3 Bucket
- **Bucket Name**: `back-talk-dev-recordings`
- **Region**: `us-east-1` (or your preferred region)
- **Permissions**: See IAM policy in main documentation

### 3. IAM Permissions
Your AWS user needs these permissions:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:HeadObject",
                "s3:CopyObject"
            ],
            "Resource": "arn:aws:s3:::back-talk-dev-recordings/*"
        }
    ]
}
```

## 🧪 Testing Your Setup

### Option 1: Run the Test Suite
```bash
# From your project root
npx ts-node src/app/api/v1/_components/s3/test-s3.ts
```

### Option 2: Manual API Testing
Test the webhook endpoints that use S3:

1. **Recording Webhook**: Send a test recording
2. **Transcription Webhook**: Send a test transcription
3. **Conversation API**: Check voicemail data includes audio URLs

### Option 3: Individual Function Testing
```typescript
import { uploadRecording, generatePresignedUrl } from './s3-service';

// Test upload
const result = await uploadRecording({
  recordingUrl: 'https://your-test-audio-file.wav',
  clientId: 'test-client',
  agentId: 'test-agent',
  callId: 'test-call',
  voicemailId: 'test-voicemail',
  leadId: 'test-lead',
});

// Test presigned URL
const audioUrl = await generatePresignedUrl({
  clientId: 'test-client',
  agentId: 'test-agent',
  voicemailId: 'test-voicemail',
});
```

## 📁 Expected S3 Structure

After successful uploads, your S3 bucket should have this structure:
```
back-talk-dev-recordings/
├── client-id-1/
│   ├── agent-id-1/
│   │   ├── voicemail-id-1.wav
│   │   └── voicemail-id-2.wav
│   └── agent-id-2/
│       └── voicemail-id-3.wav
└── client-id-2/
    └── agent-id-3/
        └── voicemail-id-4.wav
```

## 🔍 Verification Steps

### 1. Check TypeScript Compilation
```bash
npx tsc --noEmit --skipLibCheck src/app/api/v1/_components/s3/s3-service.ts
```
Should return with no errors.

### 2. Check AWS Credentials
```bash
# Test AWS CLI access (if you have it installed)
aws s3 ls s3://back-talk-dev-recordings/
```

### 3. Check Environment Variables
```typescript
console.log('AWS Region:', process.env.AWS_REGION);
console.log('AWS Access Key ID:', process.env.AWS_ACCESS_KEY_ID ? 'Set' : 'Missing');
console.log('AWS Secret Key:', process.env.AWS_SECRET_ACCESS_KEY ? 'Set' : 'Missing');
```

## 🚨 Common Issues & Solutions

### Issue: "Credentials not found"
**Solution**: Ensure AWS environment variables are set correctly.

### Issue: "Access Denied"
**Solution**: Check IAM permissions and bucket policy.

### Issue: "Bucket does not exist"
**Solution**: Create the `back-talk-dev-recordings` bucket in your AWS region.

### Issue: "Invalid signature"
**Solution**: Verify AWS credentials are correct and not expired.

### Issue: TypeScript compilation errors
**Solution**: Ensure AWS SDK v3 is installed and types are compatible.

## ✅ Ready for Production Checklist

- [ ] Environment variables configured
- [ ] AWS credentials working
- [ ] S3 bucket created with correct permissions
- [ ] Test upload successful
- [ ] Test presigned URL generation successful
- [ ] Test metadata update successful
- [ ] Webhook routes updated and tested
- [ ] UI displays voicemail audio players correctly

## 📞 Integration Points

The S3 service integrates with:

1. **Recording Webhook** (`recording/route.ts`)
   - Uploads recordings with metadata
   
2. **Transcription Webhook** (`transcription/route.ts`)
   - Updates metadata with transcription and conversation IDs
   
3. **Conversation API** (`conversations/[id]/route.ts`)
   - Generates presigned URLs for audio playback
   
4. **Message Details UI** (`MessageDetails.tsx`)
   - Displays audio players using presigned URLs

## 🎯 Success Criteria

Your S3 service is ready when:
- ✅ All TypeScript compilation passes
- ✅ Test uploads work without errors
- ✅ Presigned URLs are generated successfully
- ✅ Metadata updates work correctly
- ✅ Audio players in UI can stream recordings
- ✅ No AWS credential or permission errors
