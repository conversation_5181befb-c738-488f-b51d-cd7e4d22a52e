import {
  VoicemailMetadata,
  UploadRecordingOptions,
  UpdateRecordingMetadataOptions,
  UploadRecordingResult,
  GeneratePresignedUrlOptions
} from './types';

// These imports will work once AWS SDK is properly installed
import { S3Client, PutObjectCommand, Co<PERSON><PERSON><PERSON><PERSON>ommand, HeadObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const BUCKET_NAME = 'back-talk-dev-recordings';
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';

// Initialize S3 client (will work once AWS SDK is installed)
const s3Client = new S3Client({
  region: AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

/**
 * Generates the S3 key for a voicemail recording
 */
function generateS3Key(clientId: string, agentId: string, voicemailId: string): string {
  return `${clientId}/${agentId}/${voicemailId}.wav`;
}

/**
 * Downloads a file from a URL and returns the buffer
 */
async function downloadFile(url: string): Promise<Buffer> {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to download file: ${response.statusText}`);
  }
  const arrayBuffer = await response.arrayBuffer();
  return Buffer.from(arrayBuffer);
}

/**
 * Creates metadata object for S3 object
 */
function createMetadata(options: UploadRecordingOptions): Record<string, string> {
  return {
    'client-id': options.clientId,
    'agent-id': options.agentId,
    'call-id': options.callId,
    'voicemail-id': options.voicemailId,
    'lead-id': options.leadId,
  };
}

/**
 * Uploads a recording to S3 with metadata
 */
export async function uploadRecording(options: UploadRecordingOptions): Promise<UploadRecordingResult> {
  const { recordingUrl, clientId, agentId, voicemailId } = options;

  try {
    // Download the recording from Twilio
    const recordingBuffer = await downloadFile(recordingUrl);

    // Generate S3 key
    const s3Key = generateS3Key(clientId, agentId, voicemailId);

    // Create metadata
    const metadata = createMetadata(options);

    const putCommand = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
      Body: recordingBuffer,
      ContentType: 'audio/wav',
      Metadata: metadata,
      ServerSideEncryption: 'AES256',
    });

    await s3Client.send(putCommand);

    console.log(`Recording uploaded to S3: ${s3Key}`);

    return {
      s3Key,
      s3Url: `s3://${BUCKET_NAME}/${s3Key}`,
      bucket: BUCKET_NAME,
    };
  } catch (error) {
    console.error('Error uploading recording to S3:', error);
    throw new Error(`Failed to upload recording: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Updates the metadata of an existing S3 object
 */
export async function updateRecordingMetadata(options: UpdateRecordingMetadataOptions): Promise<void> {
  const { clientId, agentId, voicemailId, transcriptionId, conversationId } = options;

  try {
    const s3Key = generateS3Key(clientId, agentId, voicemailId);

    // First, get the current object to preserve existing metadata
    const headCommand = new HeadObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
    });

    const headResponse = await s3Client.send(headCommand);
    const currentMetadata = headResponse.Metadata || {};

    // Update metadata with new values
    const updatedMetadata: Record<string, string> = {
      ...currentMetadata,
      'client-id': clientId,
      'agent-id': agentId,
      'voicemail-id': voicemailId,
    };

    if (transcriptionId) {
      updatedMetadata['transcription-id'] = transcriptionId;
    }

    if (conversationId) {
      updatedMetadata['conversation-id'] = conversationId;
    }

    // Copy the object to itself with updated metadata
    const copyCommand = new CopyObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
      CopySource: `${BUCKET_NAME}/${s3Key}`,
      Metadata: updatedMetadata,
      MetadataDirective: 'REPLACE',
      ServerSideEncryption: 'AES256',
    });

    await s3Client.send(copyCommand);

    console.log(`Updated metadata for S3 object: ${s3Key}`);
  } catch (error) {
    console.error('Error updating S3 metadata:', error);
    throw new Error(`Failed to update recording metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generates a presigned URL for accessing a recording
 */
export async function generatePresignedUrl(options: GeneratePresignedUrlOptions): Promise<string> {
  const { clientId, agentId, voicemailId, expiresIn = 3600 } = options;

  try {
    const s3Key = generateS3Key(clientId, agentId, voicemailId);

    const getCommand = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
    });

    const presignedUrl = await getSignedUrl(s3Client, getCommand, {
      expiresIn,
    });

    return presignedUrl;

  } catch (error) {
    console.error('Error generating presigned URL:', error);
    throw new Error(`Failed to generate presigned URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Checks if a recording exists in S3
 */
export async function recordingExists(clientId: string, agentId: string, voicemailId: string): Promise<boolean> {
  try {
    const s3Key = generateS3Key(clientId, agentId, voicemailId);

    const headCommand = new HeadObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
    });

    await s3Client.send(headCommand);
    return true;

  } catch (error) {
    // If the object doesn't exist, AWS SDK throws an error
    return false;
  }
}

/**
 * Gets the metadata for a recording
 */
export async function getRecordingMetadata(clientId: string, agentId: string, voicemailId: string): Promise<VoicemailMetadata | null> {
  try {
    const s3Key = generateS3Key(clientId, agentId, voicemailId);

    const headCommand = new HeadObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
    });

    const response = await s3Client.send(headCommand);
    return response.Metadata as VoicemailMetadata || null;

  } catch (error) {
    console.error('Error getting recording metadata:', error);
    return null;
  }
}
