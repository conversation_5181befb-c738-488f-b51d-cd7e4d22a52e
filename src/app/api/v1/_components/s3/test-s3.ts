// Test file to verify S3 service functionality
// Run this to test your S3 configuration

import {
  uploadRecording,
  updateRecordingMetadata,
  generatePresignedUrl,
  recordingExists,
  getRecordingMetadata
} from './s3-service';

// Test configuration
const testConfig = {
  clientId: 'test-client-123',
  agentId: 'test-agent-456',
  voicemailId: 'test-voicemail-789',
  callId: 'test-call-abc',
  leadId: 'test-lead-def',
  // Use a small test audio file URL (you can replace with a real Twilio recording URL)
  recordingUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
};

/**
 * Test S3 upload functionality
 */
async function testUpload() {
  console.log('🧪 Testing S3 upload...');
  
  try {
    const result = await uploadRecording({
      recordingUrl: testConfig.recordingUrl,
      clientId: testConfig.clientId,
      agentId: testConfig.agentId,
      callId: testConfig.callId,
      voicemailId: testConfig.voicemailId,
      leadId: testConfig.leadId,
    });
    
    console.log('✅ Upload successful:', result);
    return true;
  } catch (error) {
    console.error('❌ Upload failed:', error);
    return false;
  }
}

/**
 * Test metadata update functionality
 */
async function testMetadataUpdate() {
  console.log('🧪 Testing metadata update...');
  
  try {
    await updateRecordingMetadata({
      clientId: testConfig.clientId,
      agentId: testConfig.agentId,
      voicemailId: testConfig.voicemailId,
      transcriptionId: 'test-transcription-123',
      conversationId: 'test-conversation-456',
    });
    
    console.log('✅ Metadata update successful');
    return true;
  } catch (error) {
    console.error('❌ Metadata update failed:', error);
    return false;
  }
}

/**
 * Test presigned URL generation
 */
async function testPresignedUrl() {
  console.log('🧪 Testing presigned URL generation...');
  
  try {
    const url = await generatePresignedUrl({
      clientId: testConfig.clientId,
      agentId: testConfig.agentId,
      voicemailId: testConfig.voicemailId,
      expiresIn: 300, // 5 minutes
    });
    
    console.log('✅ Presigned URL generated:', url);
    return true;
  } catch (error) {
    console.error('❌ Presigned URL generation failed:', error);
    return false;
  }
}

/**
 * Test recording existence check
 */
async function testRecordingExists() {
  console.log('🧪 Testing recording existence check...');
  
  try {
    const exists = await recordingExists(
      testConfig.clientId,
      testConfig.agentId,
      testConfig.voicemailId
    );
    
    console.log('✅ Recording exists check:', exists);
    return true;
  } catch (error) {
    console.error('❌ Recording exists check failed:', error);
    return false;
  }
}

/**
 * Test metadata retrieval
 */
async function testGetMetadata() {
  console.log('🧪 Testing metadata retrieval...');
  
  try {
    const metadata = await getRecordingMetadata(
      testConfig.clientId,
      testConfig.agentId,
      testConfig.voicemailId
    );
    
    console.log('✅ Metadata retrieved:', metadata);
    return true;
  } catch (error) {
    console.error('❌ Metadata retrieval failed:', error);
    return false;
  }
}

/**
 * Run all tests
 */
export async function runS3Tests() {
  console.log('🚀 Starting S3 service tests...\n');
  
  // Check environment variables
  const requiredEnvVars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars);
    console.log('Please set the following environment variables:');
    missingVars.forEach(varName => console.log(`  - ${varName}`));
    return false;
  }
  
  console.log('✅ Environment variables configured');
  console.log(`📍 AWS Region: ${process.env.AWS_REGION || 'us-east-1'}`);
  console.log(`🪣 S3 Bucket: back-talk-dev-recordings\n`);
  
  const tests = [
    { name: 'Upload', fn: testUpload },
    { name: 'Recording Exists', fn: testRecordingExists },
    { name: 'Metadata Update', fn: testMetadataUpdate },
    { name: 'Get Metadata', fn: testGetMetadata },
    { name: 'Presigned URL', fn: testPresignedUrl },
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const success = await test.fn();
    if (success) passedTests++;
    console.log(''); // Add spacing between tests
  }
  
  console.log(`📊 Test Results: ${passedTests}/${tests.length} tests passed`);
  
  if (passedTests === tests.length) {
    console.log('🎉 All tests passed! Your S3 service is ready to use.');
    return true;
  } else {
    console.log('⚠️  Some tests failed. Please check your AWS configuration.');
    return false;
  }
}

// Export test configuration for manual testing
export { testConfig };

// If running this file directly, run the tests
if (require.main === module) {
  runS3Tests().then(success => {
    process.exit(success ? 0 : 1);
  });
}
