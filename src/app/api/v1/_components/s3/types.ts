export interface VoicemailMetadata extends Record<string, string> {
  'client-id': string;
  'agent-id': string;
  'call-id': string;
  'voicemail-id': string;
  'lead-id': string;
  'transcription-id'?: string;
  'conversation-id'?: string;
}

export interface UploadRecordingOptions {
  recordingUrl: string;
  clientId: string;
  agentId: string;
  callId: string;
  voicemailId: string;
  leadId: string;
}

export interface UpdateRecordingMetadataOptions {
  clientId: string;
  agentId: string;
  voicemailId: string;
  transcriptionId?: string;
  conversationId?: string;
}

export interface UploadRecordingResult {
  s3Key: string;
  s3Url: string;
  bucket: string;
}

export interface GeneratePresignedUrlOptions {
  clientId: string;
  agentId: string;
  voicemailId: string;
  expiresIn?: number; // seconds, default 3600 (1 hour)
}
