import {AutoRespondResponseDelay, CompanySize} from '@/../generated/prisma';

export interface ChatMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

export interface OpenAIModelConfig {
  model?: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
}

export interface AgentContext {
  id: string;
  client: {
    id: string;
    name: string;
    company_size?: CompanySize | null;
    business_type?: {
      name: string;
    } | null;
    offers_emergency?: boolean | null;
    is_mobile?: boolean | null;
  };
  phone_number_id: string;
  auto_responses: Array<{
    id: string;
    agent_id: string;
    created_at: Date | null;
    message_prompt: string;
    enabled: boolean | null;
    response_delay: AutoRespondResponseDelay;
  }>;
}

export interface ConversationContext {
  id: string;
  messages: Array<{
    body: string | null;
    direction: string;
    created_at: Date;
  }>;
}

export interface LeadContext {
  id: string;
  phone_number_id: string;
  name?: string | null;
}

export interface OpenAIAgentOptions {
  agentId: string;
  customPrompt?: string;
  modelConfig?: OpenAIModelConfig;
  conversationHistory?: ConversationContext;
  leadContext?: LeadContext;
  scenario?: 'sms' | 'voicemail';
  userMessage?: string;
  transcriptionText?: string;
}

export interface OpenAIAgentResponse {
  response: string;
  agentContext: AgentContext;
  conversationContext?: ConversationContext;
  leadContext?: LeadContext;
}

export interface CompanySizeMapping {
  [key: string]: string;
}
