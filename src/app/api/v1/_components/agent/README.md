# OpenAI Agent Component

A reusable component for generating AI responses in agent interactions, supporting both SMS and voicemail scenarios.

## Features

- **Reusable**: Works for both SMS and voicemail transcription responses
- **Configurable prompts**: Accept custom prompts with sensible defaults
- **Model tuning**: Configurable OpenAI parameters (temperature, frequency_penalty, etc.)
- **Agent context**: Automatically loads agent and company information
- **Conversation memory**: Uses previous message history for context
- **Business-aware**: Understands company size, services, and business type
- **SMS-optimized**: Generates appropriate responses for SMS communication

## Usage

### Basic Usage

```typescript
import { generateAgentResponse } from './openai-agent';

const result = await generateAgentResponse({
  agentId: 'agent-uuid',
  scenario: 'sms',
  userMessage: 'Hello, I need help with my plumbing',
});

console.log(result.response); // AI-generated response
```

### Advanced Usage with Custom Configuration

```typescript
import { generateAgentResponse } from './openai-agent';

const result = await generateAgentResponse({
  agentId: 'agent-uuid',
  scenario: 'voicemail',
  transcriptionText: 'Hi, my sink is leaking...',
  userMessage: 'I just left you a voicemail.',
  customPrompt: 'You are a friendly plumber assistant...',
  modelConfig: {
    temperature: 0.8,
    max_tokens: 120,
    frequency_penalty: 0.1,
  },
  conversationHistory: {
    id: 'conversation-uuid',
    messages: previousMessages
  },
  leadContext: {
    id: 'lead-uuid',
    phone_number_id: 'phone-uuid',
    name: 'John Doe'
  }
});
```

## API Reference

### `generateAgentResponse(options: OpenAIAgentOptions): Promise<OpenAIAgentResponse>`

#### Options

- `agentId` (string): Required. The agent ID to load context for
- `scenario` ('sms' | 'voicemail'): The type of interaction (default: 'sms')
- `customPrompt` (string): Optional custom prompt to override defaults
- `modelConfig` (OpenAIModelConfig): Optional OpenAI model configuration
- `conversationHistory` (ConversationContext): Optional conversation history for context
- `leadContext` (LeadContext): Optional lead information
- `userMessage` (string): The user's message to respond to
- `transcriptionText` (string): For voicemail scenarios, the transcription text

#### Response

- `response` (string): The AI-generated response
- `agentContext` (AgentContext): The loaded agent context
- `conversationContext` (ConversationContext): The conversation context used
- `leadContext` (LeadContext): The lead context used

### `getResponseDelayMs(delayType: string): number`

Helper function to convert delay types to milliseconds for response timing.

## Bot Behavior

The OpenAI bot is configured to:

- Act as a personable human in the service industry
- Work to get customers to wait for callbacks
- Avoid providing pricing or technical instructions
- Use casual, short SMS-sized messages
- Avoid special characters like em-dashes
- Be kind and empathetic
- Adjust professionalism based on company size

## Error Handling

The component will throw errors for:
- Agent not found
- Auto-responses disabled for the agent
- OpenAI API errors

Make sure to wrap calls in try-catch blocks for proper error handling.
