// Example usage of the OpenAI Agent component
// This file demonstrates how to use the refactored component

import { generateAgentResponse, getResponseDelayMs } from './openai-agent';

// Example 1: Basic SMS response
async function exampleSMSResponse() {
  try {
    const result = await generateAgentResponse({
      agentId: 'your-agent-id',
      scenario: 'sms',
      userMessage: 'Hello, I need help with my plumbing issue',
    });

    console.log('AI Response:', result.response);
    console.log('Agent Context:', result.agentContext);
  } catch (error) {
    console.error('Error generating SMS response:', error);
  }
}

// Example 2: Voicemail response with transcription
async function exampleVoicemailResponse() {
  try {
    const result = await generateAgentResponse({
      agentId: 'your-agent-id',
      scenario: 'voicemail',
      transcriptionText: 'Hi, my kitchen sink is leaking and I need someone to come fix it',
      userMessage: 'I just left you a voicemail.',
    });

    console.log('AI Response:', result.response);
  } catch (error) {
    console.error('Error generating voicemail response:', error);
  }
}

// Example 3: Advanced usage with custom configuration
async function exampleAdvancedUsage() {
  try {
    const result = await generateAgentResponse({
      agentId: 'your-agent-id',
      scenario: 'sms',
      userMessage: 'Can you help me with my emergency plumbing issue?',
      customPrompt: 'You are a friendly and professional plumbing assistant. Always be helpful and empathetic.',
      modelConfig: {
        temperature: 0.8,
        max_tokens: 120,
        frequency_penalty: 0.1,
        presence_penalty: 0.1,
      },
      conversationHistory: {
        id: 'conversation-id',
        messages: [
          {
            body: 'Hello, I have a plumbing issue',
            direction: 'INBOUND',
            created_at: new Date(),
          },
          {
            body: 'Hi! I\'d be happy to help. Can you tell me more about the issue?',
            direction: 'OUTBOUND',
            created_at: new Date(),
          }
        ]
      },
      leadContext: {
        id: 'lead-id',
        phone_number_id: 'phone-id',
        name: 'John Doe'
      }
    });

    console.log('AI Response:', result.response);
  } catch (error) {
    console.error('Error generating advanced response:', error);
  }
}

// Example 4: Using response delay
async function exampleWithDelay() {
  const delayMs = getResponseDelayMs('RANDOM_SECONDS');
  console.log(`Waiting ${delayMs}ms before sending response...`);
  
  await new Promise(resolve => setTimeout(resolve, delayMs));
  
  // Then generate and send response
  const result = await generateAgentResponse({
    agentId: 'your-agent-id',
    scenario: 'sms',
    userMessage: 'Thanks for getting back to me!',
  });

  console.log('Delayed response:', result.response);
}

// Export examples for testing
export {
  exampleSMSResponse,
  exampleVoicemailResponse,
  exampleAdvancedUsage,
  exampleWithDelay
};
