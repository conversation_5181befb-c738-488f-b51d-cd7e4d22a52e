import { OpenAI } from 'openai';
import { PrismaClient, CompanySize } from '@/../generated/prisma';
import {
  ChatMessage,
  OpenAIModelConfig,
  AgentContext,
  ConversationContext,
  LeadContext,
  OpenAIAgentOptions,
  OpenAIAgentResponse,
  CompanySizeMapping
} from './types';

const prisma = new PrismaClient();
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Default model configuration
const DEFAULT_MODEL_CONFIG: OpenAIModelConfig = {
  model: "gpt-4.1-mini",
  temperature: 0.93,
  max_tokens: 160,
  top_p: 1,
  frequency_penalty: 0,
  presence_penalty: 0,
};

// Company size mapping for prompts
const COMPANY_SIZE_MAPPING: CompanySizeMapping = {
  [CompanySize.SOLO]: '1',
  [CompanySize.MICRO]: '2-10',
  [CompanySize.SMALL]: '11-50',
  [CompanySize.MEDIUM]: '51-200',
  [CompanySize.LARGE]: '201-1000',
  [CompanySize.ENTERPRISE]: '1000+',
};

// Default prompts for different scenarios
const DEFAULT_PROMPTS = {
  sms: "I'm an AI assistant. How can I help you?",
  voicemail: "I'm an AI assistant. How can I help you?",
};

/**
 * Loads agent context including client and business information
 */
async function loadAgentContext(agentId: string): Promise<AgentContext> {
  const agent = await prisma.agent.findUnique({
    where: { id: agentId },
    include: {
      client: {
        include: {
          business_type: true
        }
      },
      auto_responses: {
        orderBy: {
          created_at: 'desc',
        },
        take: 1,
      },
    },
  });

  if (!agent) {
    throw new Error(`Agent not found with ID ${agentId}`);
  }

  return {
    id: agent.id,
    client: {
      id: agent.client.id,
      name: agent.client.name || 'Unknown Business',
      company_size: agent.client.company_size,
      business_type: agent.client.business_type,
      offers_emergency: agent.client.offers_emergency,
      is_mobile: agent.client.is_mobile,
    },
    phone_number_id: agent.phone_number_id,
    auto_responses: agent.auto_responses,
  };
}

/**
 * Generates the company size string for prompts
 */
function getCompanySizeString(companySize?: CompanySize | null): string {
  if (!companySize) return '1';
  return COMPANY_SIZE_MAPPING[companySize] || '1';
}

/**
 * Builds the system prompt based on agent context and scenario
 */
function buildSystemPrompt(
  agentContext: AgentContext,
  scenario: 'sms' | 'voicemail',
  customPrompt?: string,
  transcriptionText?: string
): string {
  const prompt = customPrompt ||
    agentContext.auto_responses[0]?.message_prompt ||
    DEFAULT_PROMPTS[scenario];

  const companySize = getCompanySizeString(agentContext.client.company_size);
  const businessType = agentContext.client.business_type?.name || 'service';
  const emergencyServices = agentContext.client.offers_emergency ? '' : 'not ';
  const mobileServices = agentContext.client.is_mobile ? '' : 'not ';

  const baseInstructions = `You are the on-call representative working on behalf of a business named ${agentContext.client.name}, you've received a ${scenario === 'voicemail' ? 'voicemail' : 'message'} from a potential lead. We need you to appear as human as possible.
The business is a ${businessType} company with the business size of ${companySize} employees.
Your company does ${emergencyServices}offer emergency services.
Your company does ${mobileServices}offer mobile response services.
Base the responses on the following message prompt from the company: "${prompt}"`;

  if (scenario === 'voicemail') {
    return `${baseInstructions}
Please create a very casual SMS response that lets the lead know that you received their voicemail and will get back to them soon.
${transcriptionText ? `The voicemail transcription is: "${transcriptionText}"` : ''}
Ask if they have more info about their needs that they can text you.
Do not give the lead instructions on how to solve the problem.
Do not give pricing.
Do not use characters that cannot be typed on a smartphone, such as "—"
Be kind and empathetic to the lead.
Remember to be very casual and produce short messages as this is an SMS response.`;
  } else {
    return `${baseInstructions}
Please create a very casual response that lets the lead know that you're currently busy.
The lead might want to send more information, if so hold a conversation with them to win over the business. If they ask to call tell them you'll call them shortly.
Ask if they have more info about the issue.
If the lead has already provided more information stop asking for them to send more information.
Do not give the lead instructions on how to solve the problem.
Do not give pricing.
Do not use characters that cannot be typed on a smartphone, such as "—"
Be kind and empathetic to the lead.
Remember to be very casual and produce short messages as this is SMS responses.`;
  }
}

/**
 * Builds conversation history for context
 */
function buildConversationHistory(
  systemPrompt: string,
  conversationContext?: ConversationContext,
  userMessage?: string
): ChatMessage[] {
  const messages: ChatMessage[] = [
    { role: "system", content: systemPrompt }
  ];

  // Add conversation history if available
  if (conversationContext?.messages) {
    const historyMessages = conversationContext.messages
      .slice(-10) // Limit to last 10 messages to avoid token limits
      .map((msg): ChatMessage => ({
        role: msg.direction === 'INBOUND' ? 'user' : 'assistant',
        content: msg.body || "",
      }));

    messages.push(...historyMessages);
  }

  // Add current user message
  if (userMessage) {
    messages.push({ role: "user", content: userMessage });
  }

  return messages;
}

/**
 * Main function to generate OpenAI response for agent interactions
 */
export async function generateAgentResponse(options: OpenAIAgentOptions): Promise<OpenAIAgentResponse> {
  const {
    agentId,
    customPrompt,
    modelConfig = {},
    conversationHistory,
    leadContext,
    scenario = 'sms',
    userMessage,
    transcriptionText
  } = options;

  // Load agent context
  const agentContext = await loadAgentContext(agentId);

  // Check if auto-responses are enabled
  if (!agentContext.auto_responses.length || !agentContext.auto_responses[0].enabled) {
    throw new Error('Auto-responses are disabled for this agent');
  }

  // Build system prompt
  const systemPrompt = buildSystemPrompt(agentContext, scenario, customPrompt, transcriptionText);

  // Build conversation history
  const messages = buildConversationHistory(systemPrompt, conversationHistory, userMessage);

  // Merge model configuration with defaults
  const finalModelConfig = { ...DEFAULT_MODEL_CONFIG, ...modelConfig };

  // Generate AI response
  const completion = await openai.chat.completions.create({
    model: finalModelConfig.model!,
    messages,
    temperature: finalModelConfig.temperature,
    max_tokens: finalModelConfig.max_tokens,
    top_p: finalModelConfig.top_p,
    frequency_penalty: finalModelConfig.frequency_penalty,
    presence_penalty: finalModelConfig.presence_penalty,
  });

  const response = completion.choices?.[0]?.message?.content?.trim() ||
    (scenario === 'voicemail'
      ? 'Thanks for your voicemail. I\'ll get back to you as soon as possible.'
      : 'Sorry, I was unable to generate a response at this time.');

  return {
    response,
    agentContext,
    conversationContext: conversationHistory,
    leadContext,
  };
}

/**
 * Helper function to get response delay in milliseconds
 */
export function getResponseDelayMs(delayType: string): number {
  switch (delayType) {
    case 'RANDOM_SECONDS':
      return Math.floor(Math.random() * 30) * 1000; // 0-30 seconds
    case 'ONE_MINUTE':
      return 60 * 1000;
    case 'FIVE_MINUTES':
      return 5 * 60 * 1000;
    case 'TEN_MINUTES':
      return 10 * 60 * 1000;
    case 'THIRTY_MINUTES':
      return 30 * 60 * 1000;
    case 'RANDOM_MINUTES':
      return Math.floor(Math.random() * 10 + 1) * 60 * 1000; // 1-10 minutes
    case 'ONE_HOUR':
      return 60 * 60 * 1000;
    case 'IMMEDIATE':
    default:
      return 0;
  }
}
