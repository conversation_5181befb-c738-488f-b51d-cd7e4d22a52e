# API Components

This directory contains reusable components for the Back Talk API.

## Components

### Agent (`/agent`)
Reusable OpenAI agent component for generating AI responses in agent interactions.

**Features:**
- Supports both SMS and voicemail scenarios
- Configurable prompts and model parameters
- Automatic agent and company context loading
- Conversation memory support
- Business-aware responses

### Lead (`/lead`)
Reusable lead management component for finding and creating leads.

**Features:**
- Find or create leads by phone number
- Find or create conversations between agents and leads
- Update lead notes
- Phone number normalization and handling

### S3 (`/s3`)
AWS S3 service for managing voicemail recordings.

**Features:**
- Upload recordings to S3 with metadata
- Update recording metadata (transcription ID, conversation ID)
- Generate presigned URLs for audio playback
- Structured S3 key generation (`<client-id>/<agent-id>/<voicemail-id>.wav`)

## Usage Examples

### Agent Component
```typescript
import { generateAgentResponse } from '../_components/agent';

const result = await generateAgentResponse({
  agentId: 'agent-uuid',
  scenario: 'sms',
  userMessage: 'Hello, I need help',
});
```

### Lead Component
```typescript
import { findOrCreateLead, findOrCreateConversation } from '../_components/lead';

const leadResult = await findOrCreateLead({
  phoneNumber: '+15551234567',
  clientId: 'client-uuid',
  agentId: 'agent-uuid',
});

const conversationResult = await findOrCreateConversation({
  agentId: 'agent-uuid',
  leadId: leadResult.lead.id,
  clientId: 'client-uuid',
});
```

### S3 Component
```typescript
import { uploadRecording, updateRecordingMetadata, generatePresignedUrl } from '../_components/s3';

// Upload recording
const uploadResult = await uploadRecording({
  recordingUrl: 'https://api.twilio.com/recording.wav',
  clientId: 'client-uuid',
  agentId: 'agent-uuid',
  callId: 'call-uuid',
  voicemailId: 'voicemail-uuid',
  leadId: 'lead-uuid',
});

// Update metadata
await updateRecordingMetadata({
  clientId: 'client-uuid',
  agentId: 'agent-uuid',
  voicemailId: 'voicemail-uuid',
  transcriptionId: 'transcription-uuid',
  conversationId: 'conversation-uuid',
});

// Generate presigned URL
const audioUrl = await generatePresignedUrl({
  clientId: 'client-uuid',
  agentId: 'agent-uuid',
  voicemailId: 'voicemail-uuid',
});
```

## Environment Variables

### S3 Component
- `AWS_REGION` - AWS region (default: us-east-1)
- `AWS_ACCESS_KEY_ID` - AWS access key
- `AWS_SECRET_ACCESS_KEY` - AWS secret key

### Agent Component
- `OPENAI_API_KEY` - OpenAI API key

## Dependencies

### S3 Component
Requires AWS SDK v3:
```bash
npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
```

Note: AWS SDK installation requires Node.js >= 18.18. The S3 component is structured to work once the proper Node.js version is available.

## File Structure

```
src/app/api/v1/_components/
├── agent/
│   ├── index.ts
│   ├── openai-agent.ts
│   ├── types.ts
│   ├── test-example.ts
│   └── README.md
├── lead/
│   ├── index.ts
│   ├── lead-manager.ts
│   └── types.ts
├── s3/
│   ├── index.ts
│   ├── s3-service.ts
│   └── types.ts
└── README.md
```

## Integration Points

### Webhook Routes
- `recording/route.ts` - Uses S3 and Lead components
- `transcription/route.ts` - Uses Agent, S3, and Lead components  
- `sms/route.ts` - Uses Agent and Lead components

### API Routes
- `conversations/[id]/route.ts` - Uses S3 component for presigned URLs

### UI Components
- `MessageDetails.tsx` - Displays voicemail data with audio player

## Best Practices

1. **Error Handling**: All components include proper error handling and logging
2. **Type Safety**: Full TypeScript support with comprehensive type definitions
3. **Reusability**: Components are designed to be DRY and reusable across routes
4. **Performance**: Efficient database queries and S3 operations
5. **Security**: Presigned URLs for secure audio access
