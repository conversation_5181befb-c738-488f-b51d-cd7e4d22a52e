import { PrismaClient } from '@/../generated/prisma';
import { formatToE164 } from "@/components/phoneNumber/phoneFormatters";
import {
  FindOrCreateLeadOptions,
  FindOrCreateLeadResult,
  FindOrCreateConversationOptions,
  FindOrCreateConversationResult,
  LeadData,
  PhoneNumberData,
  ConversationData
} from './types';

const prisma = new PrismaClient();

/**
 * Cleans and normalizes a phone number for database storage
 */
function cleanPhoneNumber(phoneNumber: string): bigint {
  // Remove all non-digit characters and convert to BigInt
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Remove leading 1 if present (US country code)
  const withoutCountryCode = cleaned.replace(/^1/, '');

  return BigInt(withoutCountryCode);
}

/**
 * Finds or creates a lead based on phone number and client
 */
export async function findOrCreateLead(options: FindOrCreateLeadOptions): Promise<FindOrCreateLeadResult> {
  const { phoneNumber, clientId, agentId, name } = options;

  const phoneNumberBigInt = cleanPhoneNumber(phoneNumber);

  // First, try to find existing lead
  let lead = await prisma.lead.findFirst({
    where: {
      phone_number: {
        number: phoneNumberBigInt
      },
      client_id: clientId
    },
    include: {
      phone_number: true,
      person: true
    }
  });

  if (lead) {
    return {
      lead: {
        id: lead.id,
        phone_number_id: lead.phone_number_id,
        client_id: lead.client_id,
        notes: lead.notes,
        created_at: lead.created_at,
        updated_at: lead.updated_at,
        person: {
          id: lead.person.id,
          name: lead.person?.name,
          email: lead.person?.email,
          created_at: lead.person.created_at,
          updated_at: lead.person.updated_at,
        },
      },
      phoneNumber: {
        id: lead.phone_number.id,
        number: lead.phone_number.number,
        status: lead.phone_number.status,
        created_at: lead.phone_number.created_at,
        updated_at: lead.phone_number.updated_at,
      },
      isNewLead: false,
      isNewPhoneNumber: false,
    };
  }

  // Lead doesn't exist, check if phone number exists
  let leadPhoneNumber = await prisma.phone_number.findFirst({
    where: {
      number: phoneNumberBigInt
    }
  });

  let isNewPhoneNumber = false;

  // If phone number doesn't exist, create it
  if (!leadPhoneNumber) {
    leadPhoneNumber = await prisma.phone_number.create({
      data: {
        number: phoneNumberBigInt,
        status: 'ACTIVE'
      }
    });
    isNewPhoneNumber = true;
  }

  // Create the lead
  const newLead = await prisma.lead.create({
    data: {
      phone_number_id: leadPhoneNumber.id,
      client_id: clientId,
      name: name || formatToE164(phoneNumberBigInt), // Use formatted phone as default name
    }
  });

  return {
    lead: {
      id: newLead.id,
      phone_number_id: newLead.phone_number_id,
      client_id: newLead.client_id,
      name: newLead.name,
      email: newLead.email,
      notes: newLead.notes,
      created_at: newLead.created_at,
      updated_at: newLead.updated_at,
    },
    phoneNumber: {
      id: leadPhoneNumber.id,
      number: leadPhoneNumber.number,
      status: leadPhoneNumber.status,
      created_at: leadPhoneNumber.created_at,
      updated_at: leadPhoneNumber.updated_at,
    },
    isNewLead: true,
    isNewPhoneNumber,
  };
}

/**
 * Finds or creates a conversation between an agent and lead
 */
export async function findOrCreateConversation(options: FindOrCreateConversationOptions): Promise<FindOrCreateConversationResult> {
  const { agentId, leadId, clientId } = options;

  // Try to find existing conversation
  let conversation = await prisma.conversation.findFirst({
    where: {
      agent_id: agentId,
      lead_id: leadId
    },
    include: {
      messages: {
        orderBy: {
          created_at: 'asc'
        },
        take: 20 // Limit to last 20 messages for performance
      }
    }
  });

  if (conversation) {
    return {
      conversation: {
        id: conversation.id,
        agent_id: conversation.agent_id,
        lead_id: conversation.lead_id,
        client_id: conversation.client_id,
        status: conversation.status || 'ACTIVE',
        created_at: conversation.created_at,
        updated_at: conversation.updated_at,
        messages: conversation.messages.map(msg => ({
          id: msg.id,
          body: msg.body,
          direction: msg.direction,
          created_at: msg.created_at,
        }))
      },
      isNewConversation: false,
    };
  }

  // Create new conversation
  const newConversation = await prisma.conversation.create({
    data: {
      agent_id: agentId,
      lead_id: leadId,
      client_id: clientId,
      status: 'ACTIVE',
    },
    include: {
      messages: {
        orderBy: {
          created_at: 'asc'
        },
        take: 20
      }
    }
  });

  return {
    conversation: {
      id: newConversation.id,
      agent_id: newConversation.agent_id,
      lead_id: newConversation.lead_id,
      client_id: newConversation.client_id,
      status: newConversation.status || 'ACTIVE',
      created_at: newConversation.created_at,
      updated_at: newConversation.updated_at,
      messages: newConversation.messages.map(msg => ({
        id: msg.id,
        body: msg.body,
        direction: msg.direction,
        created_at: msg.created_at,
      }))
    },
    isNewConversation: true,
  };
}

/**
 * Updates lead notes with additional information
 */
export async function updateLeadNotes(leadId: string, additionalNotes: string): Promise<void> {
  const lead = await prisma.lead.findUnique({
    where: { id: leadId }
  });

  if (!lead) {
    throw new Error(`Lead not found with ID ${leadId}`);
  }

  const updatedNotes = lead.notes
    ? `${lead.notes}\n\n${additionalNotes}`
    : additionalNotes;

  await prisma.lead.update({
    where: { id: leadId },
    data: {
      notes: updatedNotes,
      updated_at: new Date()
    }
  });
}

/**
 * Gets lead by phone number and client
 */
export async function getLeadByPhoneAndClient(phoneNumber: string, clientId: string): Promise<LeadData | null> {
  const phoneNumberBigInt = cleanPhoneNumber(phoneNumber);

  const lead = await prisma.lead.findFirst({
    where: {
      phone_number: {
        number: phoneNumberBigInt
      },
      client_id: clientId
    }
  });

  if (!lead) {
    return null;
  }

  return {
    id: lead.id,
    phone_number_id: lead.phone_number_id,
    client_id: lead.client_id,
    notes: lead.notes,
    created_at: lead.created_at,
    updated_at: lead.updated_at,
    person: {
      id: lead.person.id,
      name: lead.person?.name,
      email: lead.person?.email,
      created_at: lead.person.created_at,
      updated_at: lead.person.updated_at,
    },
  };
}
