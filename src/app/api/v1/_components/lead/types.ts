import { PrismaClient } from '@/../generated/prisma';
export interface PersonData {
  id: string;
  name?: string | null;
  email?: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface LeadData {
  id: string;
  phone_number_id: string;
  client_id: string;
  notes?: string | null;
  person: PersonData;
  created_at: Date;
  updated_at: Date;
}

export interface PhoneNumberData {
  id: string;
  number: bigint;
  status: string;
  created_at: Date;
  updated_at: Date;
}

export interface FindOrCreateLeadOptions {
  phoneNumber: string; // Raw phone number string (e.g., "+15551234567")
  clientId: string;
  agentId: string;
  name?: string;
  email?: string;
}

export interface FindOrCreateLeadResult {
  lead: LeadData;
  phoneNumber: PhoneNumberData;
  isNewLead: boolean;
  isNewPhoneNumber: boolean;
}

export interface ConversationData {
  id: string;
  agent_id: string;
  lead_id: string;
  client_id: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  messages: Array<{
    id: string;
    body: string | null;
    direction: string;
    created_at: Date;
  }>;
}

export interface FindOrCreateConversationOptions {
  agentId: string;
  leadId: string;
  clientId: string;
}

export interface FindOrCreateConversationResult {
  conversation: ConversationData;
  isNewConversation: boolean;
}
