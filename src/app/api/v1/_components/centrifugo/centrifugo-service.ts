import {
  CentrifugoConfig,
  CentrifugoMessage,
  MessageData,
  ConversationUpdateData,
  PublishOptions,
  BroadcastMessageOptions,
  BroadcastConversationUpdateOptions,
  CentrifugoToken
} from './types';

import jwt from 'jsonwebtoken';

const CENTRIFUGO_CONFIG: CentrifugoConfig = {
  url: process.env.CENTRIFUGO_URL || 'ws://localhost:8001/connection/websocket',
  apiKey: process.env.CENTRIFUGO_API_KEY || 'your-api-key-here',
  tokenHmacSecretKey: process.env.CENTRIFUGO_TOKEN_HMAC_SECRET_KEY || 'your-secret-key-here',
};

/**
 * Generates a channel name for a conversation
 */
export function getConversationChannel(conversationId: string): string {
  return `conversations:${conversationId}`;
}

/**
 * Generates a JWT token for Centrifugo authentication
 */
export function generateCentrifugoToken(userId: string, conversationIds: string[] = []): string {
  const now = Math.floor(Date.now() / 1000);
  const exp = now + (24 * 60 * 60); // 24 hours

  const payload: CentrifugoToken = {
    sub: userId,
    exp: exp,
    iat: now,
    channels: conversationIds.map(id => getConversationChannel(id))
  };

  return jwt.sign(payload, CENTRIFUGO_CONFIG.tokenHmacSecretKey);
}

/**
 * Publishes a message to Centrifugo via HTTP API
 */
export async function publishToCentrifugo(options: PublishOptions): Promise<boolean> {
  try {
    const response = await fetch(`${process.env.CENTRIFUGO_HTTP_URL || 'http://localhost:8000'}/api/publish`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `apikey ${CENTRIFUGO_CONFIG.apiKey}`,
      },
      body: JSON.stringify({
        channel: options.channel,
        data: options.data,
      }),
    });

    if (!response.ok) {
      throw new Error(`Centrifugo publish failed: ${response.statusText}`);
    }

    console.log(`Published to channel ${options.channel}:`, options.data);
    return true;
  } catch (error) {
    console.error('Error publishing to Centrifugo:', error);
    return false;
  }
}

/**
 * Broadcasts a new message to all subscribers of a conversation
 */
export async function broadcastMessage(options: BroadcastMessageOptions): Promise<boolean> {
  const channel = getConversationChannel(options.conversationId);
  
  const message: CentrifugoMessage = {
    type: 'message',
    data: options.message,
    timestamp: Date.now(),
  };

  return await publishToCentrifugo({
    channel,
    data: message,
  });
}

/**
 * Broadcasts a conversation update to all subscribers
 */
export async function broadcastConversationUpdate(options: BroadcastConversationUpdateOptions): Promise<boolean> {
  const channel = getConversationChannel(options.conversationId);
  
  const message: CentrifugoMessage = {
    type: 'conversation_update',
    data: options.update,
    timestamp: Date.now(),
  };

  return await publishToCentrifugo({
    channel,
    data: message,
  });
}

/**
 * Broadcasts typing indicator
 */
export async function broadcastTyping(conversationId: string, userId: string, isTyping: boolean): Promise<boolean> {
  const channel = getConversationChannel(conversationId);
  
  const message: CentrifugoMessage = {
    type: 'typing',
    data: {
      conversation_id: conversationId,
      user_id: userId,
      is_typing: isTyping,
    },
    timestamp: Date.now(),
  };

  return await publishToCentrifugo({
    channel,
    data: message,
  });
}

/**
 * Broadcasts presence update
 */
export async function broadcastPresence(conversationId: string, userId: string, online: boolean): Promise<boolean> {
  const channel = getConversationChannel(conversationId);
  
  const message: CentrifugoMessage = {
    type: 'presence',
    data: {
      conversation_id: conversationId,
      user_id: userId,
      online,
      last_seen: online ? undefined : new Date().toISOString(),
    },
    timestamp: Date.now(),
  };

  return await publishToCentrifugo({
    channel,
    data: message,
  });
}

/**
 * Gets channel info from Centrifugo
 */
export async function getChannelInfo(channel: string): Promise<any> {
  try {
    const response = await fetch(`${process.env.CENTRIFUGO_HTTP_URL || 'http://localhost:8000'}/api/info`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `apikey ${CENTRIFUGO_CONFIG.apiKey}`,
      },
      body: JSON.stringify({
        channel,
      }),
    });

    if (!response.ok) {
      throw new Error(`Centrifugo info failed: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting channel info:', error);
    return null;
  }
}

/**
 * Gets presence info for a conversation
 */
export async function getConversationPresence(conversationId: string): Promise<any> {
  const channel = getConversationChannel(conversationId);
  
  try {
    const response = await fetch(`${process.env.CENTRIFUGO_HTTP_URL || 'http://localhost:8000'}/api/presence`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `apikey ${CENTRIFUGO_CONFIG.apiKey}`,
      },
      body: JSON.stringify({
        channel,
      }),
    });

    if (!response.ok) {
      throw new Error(`Centrifugo presence failed: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting conversation presence:', error);
    return null;
  }
}

/**
 * Disconnects a user from all channels
 */
export async function disconnectUser(userId: string): Promise<boolean> {
  try {
    const response = await fetch(`${process.env.CENTRIFUGO_HTTP_URL || 'http://localhost:8000'}/api/disconnect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `apikey ${CENTRIFUGO_CONFIG.apiKey}`,
      },
      body: JSON.stringify({
        user: userId,
      }),
    });

    if (!response.ok) {
      throw new Error(`Centrifugo disconnect failed: ${response.statusText}`);
    }

    console.log(`Disconnected user ${userId}`);
    return true;
  } catch (error) {
    console.error('Error disconnecting user:', error);
    return false;
  }
}
