export interface CentrifugoConfig {
  url: string;
  apiKey: string;
  tokenHmacSecretKey: string;
}

export interface CentrifugoMessage {
  type: 'message' | 'conversation_update' | 'typing' | 'presence';
  data: any;
  timestamp: number;
}

export interface MessageData {
  id: string;
  conversation_id: string;
  body: string;
  direction: 'INBOUND' | 'OUTBOUND';
  created_at: string;
  sender?: {
    id: string;
    name?: string;
    phone_number?: string;
  };
}

export interface ConversationUpdateData {
  id: string;
  status: string;
  updated_at: string;
  last_message?: MessageData;
}

export interface TypingData {
  conversation_id: string;
  user_id: string;
  is_typing: boolean;
}

export interface PresenceData {
  conversation_id: string;
  user_id: string;
  online: boolean;
  last_seen?: string;
}

export interface PublishOptions {
  channel: string;
  data: any;
}

export interface SubscribeOptions {
  channel: string;
  token?: string;
}

export interface CentrifugoToken {
  sub: string; // subject (user ID)
  exp: number; // expiration timestamp
  iat: number; // issued at timestamp
  channels?: string[]; // allowed channels
}

export interface BroadcastMessageOptions {
  conversationId: string;
  message: MessageData;
  excludeUserId?: string;
}

export interface BroadcastConversationUpdateOptions {
  conversationId: string;
  update: ConversationUpdateData;
  excludeUserId?: string;
}
