# Testing Centrifugo Real-Time Messaging

This guide helps you test the Centrifugo real-time messaging integration in the Back Talk application.

## Quick Setup for Testing

### 1. Start Centrifugo (Placeholder Mode)

Since Node.js version requirements prevent full Centrifugo client installation, the current implementation provides:

- ✅ **Server-side broadcasting** - API endpoints can publish to Centrifugo
- ✅ **Message storage** - Messages are stored in database
- ✅ **UI structure** - Real-time UI components are ready
- ⏳ **Client-side WebSocket** - Placeholder until Node.js >= 18.18

```bash
# Start Centrifugo server
docker-compose up -d centrifugo

# Check if running
docker-compose ps centrifugo
```

### 2. Test Server-Side Broadcasting

The server can already broadcast messages to Centrifugo:

1. **Send SMS** - Incoming SMS messages are broadcast to conversation channels
2. **Send Messages** - Outgoing messages via API are broadcast
3. **Update Status** - Conversation status changes are broadcast

### 3. Test with Development Panel

In development mode, a test panel appears in conversation details:

1. Navigate to any conversation: `/conversations?conversationId=some-id`
2. Look for the yellow "🧪 Centrifugo Test Panel" 
3. Click "Test Token Generation" to verify API works
4. Send test messages to verify broadcasting

## Current Implementation Status

### ✅ **Working Now**
- Centrifugo Docker container
- Server-side message broadcasting
- Database message storage
- API endpoints for messages
- UI components with connection status
- Test panel for development

### ⏳ **Ready for Node.js Upgrade**
- Client-side WebSocket connections
- Real-time message updates
- Live conversation status changes
- Typing indicators
- Presence tracking

## Testing Scenarios

### 1. Message Broadcasting Test

```bash
# Send a test message via API
curl -X POST http://localhost:3000/api/v1/conversations/CONVERSATION_ID/messages \
  -H "Content-Type: application/json" \
  -d '{"body": "Test message from API"}'
```

Check Centrifugo logs:
```bash
docker-compose logs -f centrifugo
```

### 2. SMS Webhook Test

Send an SMS to your Twilio number and check:
1. Message appears in database
2. Centrifugo receives broadcast (check logs)
3. UI shows message (after page refresh for now)

### 3. Conversation Status Test

```bash
# Update conversation status
curl -X PUT http://localhost:3000/api/v1/conversations/CONVERSATION_ID \
  -H "Content-Type: application/json" \
  -d '{"status": "RESOLVED"}'
```

### 4. Token Generation Test

```bash
# Test token generation
curl -X POST http://localhost:3000/api/v1/centrifugo/token \
  -H "Content-Type: application/json" \
  -d '{"userId": "test-user", "conversationIds": ["conv-1", "conv-2"]}'
```

## Monitoring Centrifugo

### Admin Interface
Visit: http://localhost:8000/
Password: `admin123`

### API Monitoring
```bash
# Check channel info
curl -X POST http://localhost:8000/api/info \
  -H "Authorization: apikey your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{"channel": "conversations:CONVERSATION_ID"}'
```

### Logs
```bash
# Real-time logs
docker-compose logs -f centrifugo

# Check for broadcasts
docker-compose logs centrifugo | grep "publish"
```

## Expected Behavior

### Current (Placeholder Mode)
1. **Send Message** → Stored in DB + Broadcast to Centrifugo
2. **Receive SMS** → Stored in DB + Broadcast to Centrifugo  
3. **Status Change** → Updated in DB + Broadcast to Centrifugo
4. **UI Updates** → Manual refresh required

### After Node.js Upgrade
1. **Send Message** → Stored in DB + Broadcast + **Real-time UI update**
2. **Receive SMS** → Stored in DB + Broadcast + **Real-time UI update**
3. **Status Change** → Updated in DB + Broadcast + **Real-time UI update**
4. **UI Updates** → **Automatic real-time updates**

## Debugging

### Check Connection Status
In conversation details, look for:
- 🟢 **Live** - Connected and subscribed (after Node.js upgrade)
- 🔴 **Offline** - Not connected (current placeholder state)

### Console Logs
Open browser console to see:
```javascript
// Connection attempts
"Centrifugo connection placeholder - token: ..."

// Message handling
"Received new message: ..."
"Received conversation update: ..."
```

### Server Logs
Check Next.js console for:
```
Published to channel conversations:CONVERSATION_ID: {...}
Failed to broadcast message to Centrifugo: ...
```

## Troubleshooting

### Issue: "Centrifugo not receiving messages"
**Solution**: Check API key in environment variables

### Issue: "Token generation fails"
**Solution**: Verify JWT secret key configuration

### Issue: "Docker container not starting"
**Solution**: Check port conflicts (8000, 8001)

### Issue: "Messages not appearing in UI"
**Solution**: Currently expected - requires page refresh until WebSocket client is active

## Next Steps

1. **Upgrade Node.js** to >= 18.18
2. **Install centrifuge client**: `npm install centrifuge`
3. **Uncomment WebSocket code** in `useCentrifugo.ts`
4. **Test real-time updates** end-to-end

## Files Modified for Real-Time

- `src/app/conversations/ConversationDetails.tsx` - Added Centrifugo integration
- `src/app/api/v1/conversations/[id]/messages/route.ts` - Added broadcasting
- `src/app/api/v1/agents/[id]/webhook/sms/route.ts` - Added broadcasting
- `src/app/_hooks/useCentrifugo.ts` - WebSocket hook (placeholder)
- `src/app/conversations/_components/CentrifugoTest.tsx` - Test panel

The foundation is ready - real-time messaging will work immediately after Node.js upgrade! 🚀
