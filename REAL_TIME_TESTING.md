# 🚀 Real-Time Messaging Testing Guide

Now that Node.js has been upgraded and Centrifugo is running, let's test the real-time WebSocket functionality!

## ✅ Prerequisites Complete

- ✅ Node.js upgraded
- ✅ Centrifugo dependencies installed (`centrifuge`, `jsonwebtoken`)
- ✅ Centrifugo server running
- ✅ WebSocket code activated
- ✅ Environment variables configured

## 🧪 Testing Steps

### 1. Start the Next.js Application

```bash
npm run dev
```

### 2. Navigate to a Conversation

1. Go to: `http://localhost:3000/conversations`
2. Select any conversation
3. Look for the **yellow test panel** at the top (development mode only)

### 3. Check Connection Status

In the test panel, you should see:

- **WebSocket**: 🟢 Connected / 🔴 Disconnected
- **Channel**: 🟢 Subscribed / 🔴 Not subscribed
- **Real-time Activity Log** showing connection events

### 4. Test Token Generation

1. Click **"Test Token Generation"** button
2. Check the activity log for: `🎫 Token generated successfully`
3. Check browser console for token details

### 5. Test Real-Time Messaging

#### Option A: Send Test Message
1. Type a message in the test panel
2. Click **"Send Test"**
3. Watch for:
   - `✅ Test message sent` in activity log
   - `📨 Received message` in activity log (real-time echo)
   - Message appears in "Received Messages" section

#### Option B: Send SMS to Twilio Number
1. Send an SMS to your Twilio number
2. Watch for:
   - `📨 Received message` in activity log
   - Message appears in conversation (real-time)
   - No page refresh needed!

### 6. Test Conversation Status Updates

1. Use the conversation list status buttons (Resolve/Close/Reopen)
2. Watch for:
   - `🔄 Conversation updated` in activity log
   - Status changes in real-time across all open tabs

## 🔍 What to Look For

### ✅ Success Indicators

1. **Connection Status**: Both WebSocket and Channel show 🟢 Connected/Subscribed
2. **Activity Log**: Shows connection events and message flow
3. **Real-time Updates**: Messages appear without page refresh
4. **Console Logs**: No WebSocket errors in browser console

### ❌ Troubleshooting

#### Issue: "WebSocket: Disconnected"
**Possible Causes:**
- Centrifugo not running: `docker-compose ps centrifugo`
- Wrong WebSocket URL in environment variables
- Network/firewall blocking WebSocket connections

**Solutions:**
```bash
# Check Centrifugo status
docker-compose logs centrifugo

# Restart Centrifugo
docker-compose restart centrifugo

# Check environment variables
echo $NEXT_PUBLIC_CENTRIFUGO_WS_URL
```

#### Issue: "Channel: Not subscribed"
**Possible Causes:**
- Token generation failing
- Invalid API keys
- Channel permissions

**Solutions:**
1. Test token generation button
2. Check API keys in `.env` match `centrifugo/config.json`
3. Check browser console for authentication errors

#### Issue: "No real-time messages"
**Possible Causes:**
- WebSocket connected but broadcasting failing
- API key mismatch between client and server

**Solutions:**
1. Check server logs for broadcast errors
2. Verify API keys are consistent
3. Test with the test panel first

## 🎯 Expected Behavior

### Real-Time Message Flow

```
SMS Received → Twilio Webhook → Store in DB → Broadcast to Centrifugo → WebSocket → UI Update
```

### Real-Time Status Flow

```
Status Change → API Update → Store in DB → Broadcast to Centrifugo → WebSocket → UI Update
```

## 📊 Monitoring

### Browser Console
Open DevTools Console to see:
```javascript
Connecting to Centrifugo...
Connected to Centrifugo
Subscribed to conversations:conversation-id
🎉 Real-time message received: {...}
```

### Centrifugo Logs
```bash
docker-compose logs -f centrifugo
```

Look for:
- Client connections
- Channel subscriptions
- Message publications

### Network Tab
Check WebSocket connection in DevTools Network tab:
- Status: 101 Switching Protocols
- Type: websocket
- Messages flowing in real-time

## 🚀 Advanced Testing

### Multi-Tab Testing
1. Open conversation in multiple browser tabs
2. Send message from one tab
3. Watch it appear in all tabs instantly

### Mobile Testing
1. Open conversation on mobile device
2. Send SMS from another phone
3. Watch message appear in real-time

### Load Testing
1. Open multiple conversations
2. Send messages to different numbers
3. Verify each conversation gets only its messages

## 🎉 Success Criteria

Your real-time messaging is working when:

- ✅ WebSocket connects automatically
- ✅ Messages appear instantly without refresh
- ✅ Status changes sync across all clients
- ✅ No console errors
- ✅ Test panel shows all green indicators
- ✅ SMS messages appear in real-time
- ✅ Multiple tabs stay in sync

## 🔧 Development Tools

### Test Panel Features
- **Connection monitoring** - Real-time status indicators
- **Activity logging** - See all WebSocket events
- **Message testing** - Send test messages
- **Token testing** - Verify authentication
- **Received messages** - See real-time message flow

### Console Commands
```javascript
// Check Centrifugo connection (in browser console)
window.centrifugo?.state

// Manual message test
fetch('/api/v1/conversations/CONVERSATION_ID/messages', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ body: 'Test message' })
})
```

## 🎊 You're Live!

Once you see messages appearing in real-time without page refreshes, your Centrifugo integration is fully operational! 

The conversation details now provide a true real-time messaging experience. 🚀
