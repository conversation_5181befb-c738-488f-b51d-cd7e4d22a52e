Create a NextJS application with TypeScript called "Back Talk" that integrates with Twilio's API. The application should:

1. Allow users to purchase/provision a phone number through Twilio
2. Configure the purchased number to automatically respond to missed calls
3. When someone calls the number and there's no answer, the system should automatically send an SMS reply saying: "Sorry I'm busy and can't talk, how can I help?"
4. Provide a user interface to manage phone numbers and customize the auto-response message
5. Include proper error handling and loading states for API interactions
6. Implement secure authentication for accessing the service
7. Store configuration in a secure manner, avoiding exposure of API keys in client-side code

Please provide guidance on the component structure, necessary API endpoints, and how to properly implement this using NextJs and TypeScript.

Please also provide docker files that will launch Postgresql, MongoDB for caching, and a nodejs server for the application.
The backend api's should live in it's own container, so that the frontend and backend can be scaled independently

the user interface should look like the one in this mockup https://cdn.dribbble.com/userupload/16467706/file/original-a0a8888cf1fc8a046b657338365ae367.png?resize=3201x10752&vertical=center
