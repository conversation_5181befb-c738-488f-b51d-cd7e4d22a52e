{"name": "back-talk", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.839.0", "@aws-sdk/s3-request-presigner": "^3.839.0", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.8.2", "@prisma/extension-accelerate": "^2.0.1", "centrifuge": "^5.3.5", "date-fns": "^4.1.0", "heroicons": "^2.2.0", "jsonwebtoken": "^9.0.2", "next": "15.3.2", "openai": "^5.2.0", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "twilio": "^5.7.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.17.57", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "prisma": "^6.8.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}